<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.audit.mapper.DividendAnnouncementDataMapper">

    <!-- 根据证券代码和同步日期查询分红公告数据 -->
    <select id="selectBySecurityCodesAndSyncDate" resultType="cn.sdata.om.al.audit.entity.DividendAnnouncementData">
        SELECT
            juyuan_id,
            inner_code,
            security_code,
            security_name,
            security_type,
            security_market,
            data_source_type,
            registration_date,
            ex_rights_date,
            bonus_share_payment_date,
            dividend_payment_date,
            bonus_share_ratio,
            cash_dividend_ratio,
            sync_date,
            create_time,
            update_time
        FROM dividend_announcement_data
        WHERE sync_date = #{syncDate}
        <if test="securityCodes != null and securityCodes.size > 0">
            AND security_code IN
            <foreach collection="securityCodes" item="securityCode" open="(" separator="," close=")">
                #{securityCode}
            </foreach>
        </if>
        ORDER BY security_code
    </select>

    <!-- 根据同步日期删除数据 -->
    <delete id="deleteBySyncDate">
        DELETE FROM dividend_announcement_data WHERE sync_date = #{syncDate}
    </delete>

    <!-- 批量插入分红公告数据 -->
    <insert id="batchInsert">
        INSERT INTO dividend_announcement_data (
            juyuan_id, inner_code, security_code, security_name, security_type, security_market, data_source_type,
            registration_date, ex_rights_date, bonus_share_payment_date, dividend_payment_date,
            bonus_share_ratio, cash_dividend_ratio, sync_date, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.juyuanId}, #{item.innerCode}, #{item.securityCode}, #{item.securityName},
                #{item.securityType}, #{item.securityMarket}, #{item.dataSourceType}, #{item.registrationDate}, #{item.exRightsDate},
                #{item.bonusSharePaymentDate}, #{item.dividendPaymentDate}, #{item.bonusShareRatio},
                #{item.cashDividendRatio}, #{item.syncDate}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 根据证券代码和日期范围查询分红公告数据 -->
    <select id="selectBySecurityCodeAndDateRange" resultType="cn.sdata.om.al.audit.entity.DividendAnnouncementData">
        SELECT
            juyuan_id,
            inner_code,
            security_code,
            security_name,
            security_type,
            security_market,
            data_source_type,
            registration_date,
            ex_rights_date,
            bonus_share_payment_date,
            dividend_payment_date,
            bonus_share_ratio,
            cash_dividend_ratio,
            sync_date,
            create_time,
            update_time
        FROM dividend_announcement_data
        WHERE security_code = #{securityCode}
        <if test="startDate != null and startDate != ''">
            AND (
                (ex_rights_date >= #{startDate} AND ex_rights_date &lt;= #{endDate})
                OR (bonus_share_payment_date >= #{startDate} AND bonus_share_payment_date &lt;= #{endDate})
                OR (dividend_payment_date >= #{startDate} AND dividend_payment_date &lt;= #{endDate})
            )
        </if>
        ORDER BY juyuan_id DESC
    </select>

    <!-- 根据证券代码查询分红公告数据（债券查询除权日>=数据日期，其他查询最新） -->
    <select id="selectLatestBySecurityCode" resultType="cn.sdata.om.al.audit.entity.DividendAnnouncementData">
        SELECT
            juyuan_id,
            inner_code,
            security_code,
            security_name,
            security_type,
            security_market,
            data_source_type,
            registration_date,
            ex_rights_date,
            bonus_share_payment_date,
            dividend_payment_date,
            bonus_share_ratio,
            cash_dividend_ratio,
            sync_date,
            create_time,
            update_time
        FROM dividend_announcement_data
        WHERE security_code = #{securityCode}
        AND (
            -- 债券：查询除权日大于等于数据日期的数据
            (data_source_type = 'BOND' AND ex_rights_date >= #{dataDate})
            OR
            -- 其他证券：查询最新数据（不限制日期）
            (data_source_type != 'BOND' OR data_source_type IS NULL)
        )
        ORDER BY
            CASE
                WHEN data_source_type = 'BOND' THEN ex_rights_date
                ELSE juyuan_id
            END DESC
        LIMIT 1
    </select>








    <!-- 查询已存在的聚源ID -->
    <select id="queryExistingJuyuanIds" resultType="java.lang.String">
        SELECT juyuan_id
        FROM dividend_announcement_data
        WHERE juyuan_id IN
        <foreach collection="juyuanIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>



</mapper>
