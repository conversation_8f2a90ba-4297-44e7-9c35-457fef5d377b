<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.ValuationDBMapper">

    <select id="getGenerateStatus" resultType="cn.sdata.om.al.entity.ValuationTableStatus">
        SELECT L_ZTBH                        as "productId",
               1                             as "generateStatus",
               to_char(D_YWRQ, 'yyyy-MM-dd') as "valuationDate"
        FROM hsfa.TTMP_H_GZB
        WHERE D_YWRQ = TO_DATE(#{date}, 'YYYY-MM-DD')
        group by L_ZTBH, D_YWRQ
    </select>

    <select id="getReconciliationStatus" resultType="cn.sdata.om.al.entity.ValuationTableStatus">
        select a.l_ztbh                                                                           as "productId",
               to_char(d_begin, 'yyyy-MM-dd')                                                     as "valuationDate",
               case l_dzjg
                   when 0 then 'COMPLETED'
                   when 1 then 'EXCEPTION'
                   when 2 then 'COMPLETED'
                   else 'UNCOMPLETED' end                                                         as "reconciliationStatus"
        from (select L_ZTBH, max(L_ID) as L_ID
              from hsfa.tycdz_zb
              where D_BEGIN = TO_DATE(#{date}, 'YYYY-MM-DD') and VC_WJLX = '1011'
              group by L_ZTBH) a
                 inner join (select * from hsfa.tycdz_zb where D_BEGIN = TO_DATE(#{date}, 'YYYY-MM-DD')) b
                            on a.l_id = b.l_id
    </select>

    <select id="getConfirmStatus" resultType="cn.sdata.om.al.entity.ValuationTableStatus">
        SELECT a.L_ZTBH                        as "productId",
               a.L_SFQR                        as "confirmStatus",
               to_char(a.D_YWRQ, 'yyyy-MM-dd') as "valuationDate",
               b.D_VALIDATE                    AS "confirmTime"
        FROM (SELECT L_ZTBH,
                     L_SFQR,
                     D_YWRQ
              FROM hsfa.TTMP_H_GZB
              WHERE D_YWRQ = TO_DATE(#{date}, 'YYYY-MM-DD')
              group by L_ZTBH, D_YWRQ, L_SFQR) a
                 LEFT JOIN hsfa.TRJ b ON a.L_ZTBH = b.L_ZTBH AND a.D_YWRQ = b.D_DATE
    </select>

    <select id="getStructuralTableData" resultType="cn.sdata.om.al.entity.ValuationTableData">
        SELECT *
        FROM (select to_char(a.D_YWRQ, 'yyyy-MM-dd') as "valuationDate",
                     a.VC_KMMC,
                     a.l_bh,
                     a.L_ZTBH                        as "productId",
                     b.VC_JJDM                       as "productCode",
                     b.VC_FJJJDM                     as "superiorProductCode",
                     b.VC_FJJJMC                     as "superiorProductName",
                     'structured'                    as "productType"
        from (select VC_KMMC, D_YWRQ, l_bh, L_ZTBH
              from hsfa.TTMP_H_GZB
        WHERE D_YWRQ = TO_DATE(#{date}, 'YYYY-MM-DD')
        <if test="productIds != null and productIds.size > 0">
            and L_ZTBH in (
            <foreach collection="productIds" item="productId" separator=",">
                #{productId}
            </foreach>)
        </if>
        and l_bh in (300001, 300003, 300004)) a
            left join (select L_ZTBH,
                              VC_JJDM,
                              LISTAGG(VC_FJJJDM, '_') WITHIN GROUP (ORDER BY VC_FJJJDM) AS VC_FJJJDM,
                              LISTAGG(VC_FJJJMC, '_') WITHIN GROUP (ORDER BY VC_FJJJDM) AS VC_FJJJMC
                       from hsfa.TJJXSFL
                       group by L_ZTBH, VC_JJDM) b
                      on a.L_ZTBH = b.L_ZTBH)
            PIVOT(MAX(VC_KMMC)
   for l_bh in(300001 as "netValue",
               300003 as "superiorNetValue",
               300004 as "inferiorNetValue"))
    </select>

    <select id="getFlatTableData" resultType="cn.sdata.om.al.entity.ValuationTableData">
        SELECT *
        FROM (select to_char(a.D_YWRQ, 'yyyy-MM-dd') as "valuationDate",
                     a.VC_KMMC,
                     a.l_bh,
                     a.L_ZTBH as "productId",
                     'flat' as "productType"
              from (select VC_KMMC, D_YWRQ, l_bh, L_ZTBH
                    from hsfa.TTMP_H_GZB
                    WHERE D_YWRQ = TO_DATE(#{date}, 'YYYY-MM-DD')
             <if test="productIds != null and productIds.size > 0">
                 and L_ZTBH in (
                 <foreach collection="productIds" item="productId" separator=",">
                     #{productId}
                 </foreach>)
             </if>
                 and l_bh in (300001, 300002)) a)
                 PIVOT(max(VC_KMMC)
   for l_bh in(300001 as "netValue",
               300002 as "sumNetValue"))
    </select>

    <select id="getSecondValuationData" resultType="cn.sdata.om.al.entity.SecondValuationData">
        <if test="valuationTime == 'T0'">
            select vc_kmdm as subjectCode, l_ztbh as "productId", vc_kmmc as subjectName
            from hsfa.TTMP_H_GZB t
            where D_YWRQ = TO_DATE(#{date}, 'YYYY-MM-DD')
            <if test="subjectCodes != null and subjectCodes.size > 0">
                and vc_kmdm in (<foreach collection="subjectCodes" item="subjectCode" separator=",">
                #{subjectCode}
            </foreach>)
            </if>
            group by vc_kmdm, l_ztbh,vc_kmmc
        </if>
        <if test="valuationTime == 'T1'">
            select vc_kmdm as subjectCode,vc_kmmc as subjectName,l_ztbh as "productId"
            from hsfa.TTMP_H_GZB t
            where D_YWRQ = TO_DATE(#{date}, 'YYYY-MM-DD')
            <if test="subjectCodes != null and subjectCodes.size > 0">
                and (<foreach collection="subjectCodes" item="subjectCode" separator="or">
                vc_kmdm like #{subjectCode}||'%'
            </foreach>)
            </if>
            group by vc_kmdm, vc_kmmc, l_ztbh
        </if>
    </select>

    <select id="getPortfolioNetValueWarning" resultType="cn.sdata.om.al.audit.entity.PortfolioNetValueWarning">
        SELECT
        D_YWRQ AS "dataDate",
        TO_CHAR(L_ZTBH) AS productCode,
        null AS productName,
        MAX(CASE WHEN VC_KMDM = '实收资本'        THEN EN_SZ END) AS fundShare,
        MAX(CASE WHEN VC_KMDM = '今日单位净值:'  THEN EN_SZ END) AS netValue
        FROM TTMP_H_GZB
        WHERE D_YWRQ >= TO_DATE(#{date}, 'yyyy-mm-dd') AND D_YWRQ &lt;= TO_DATE(#{date}, 'yyyy-mm-dd')
        AND VC_KMDM IN ('实收资本','今日单位净值:') AND L_ZTBH IN (
        <foreach collection="productIds" item="productId" separator=",">
            #{productId}
        </foreach>)
        GROUP BY L_ZTBH,D_YWRQ
        UNION ALL
        select D_RQ      as "dataDate",
               VC_JJDM   as "productCode",
               VC_JJMC   as "productName",
               EN_JJZFE  as "fundShare",
               EN_JJDWJZ as "netValue"
        from HSFA.TJJJZ where D_RQ = TO_DATE(#{date}, 'YYYY-MM-DD')
        <if test="productIds != null and productIds.size > 0">
            and VC_JJDM in (
            <foreach collection="productCodes" item="productCode" separator=",">
                #{productCode}
            </foreach>)
    </if>
    </select>

    <select id="getPortfolioNetValueFluctuation" resultType="cn.sdata.om.al.audit.entity.PortfolioNetValueFluctuation">
        SELECT
        D_YWRQ AS "dataDate",
        TO_CHAR(L_ZTBH) AS "productCode",
        NULL AS "productName",
        MAX(CASE WHEN VC_KMDM = '今日单位净值:'  THEN EN_SZ END) AS "currentNetValue",
        0 AS "currentTenThousandIncome"
        FROM hsfa.TTMP_H_GZB
        WHERE D_YWRQ >= TO_DATE(#{date}, 'yyyy-mm-dd') AND D_YWRQ &lt;= TO_DATE(#{date}, 'yyyy-mm-dd')
        AND VC_KMDM IN ('今日单位净值:') AND L_ZTBH IN (
        <foreach collection="productIds" item="productId" separator=",">
            #{productId}
        </foreach>)
        GROUP BY L_ZTBH,D_YWRQ
        UNION ALL
        select D_RQ AS "dataDate",
        VC_JJDM AS "productCode",
        VC_JJMC AS "productName",
        EN_JJDWJZ AS "currentNetValue",
        EN_DWJJSY AS "currentTenThousandIncome" -- 查询一特有的列
        from HSFA.TJJJZ where D_RQ = TO_DATE(#{date}, 'YYYY-MM-DD')
        <if test="productIds != null and productIds.size > 0">
            and VC_JJDM in (
            <foreach collection="productCodes" item="productCode" separator=",">
                #{productCode}
            </foreach>)
        </if>
        ORDER BY
        "dataDate",
        "productCode"
    </select>
    <select id="syncTjjjzInfo" resultType="cn.sdata.om.al.audit.entity.Tjjjz">
        SELECT
        -1 AS flag,
        L_ZTBH,
        D_YWRQ AS D_RQ,
        NULL AS VC_JJDM,
        NULL AS VC_JJMC,
        MAX(CASE WHEN VC_KMDM = '委托资产净值:' THEN EN_SZ END) AS EN_JJZJZ,
        MAX(CASE WHEN VC_KMDM = '实收资本'       THEN EN_SZ END) AS EN_JJZFE,
        MAX(CASE WHEN VC_KMDM = '今日单位净值:' THEN EN_SZ END) AS EN_JJDWJZ,
        NULL AS EN_JJSY,
        NULL AS EN_DWJJSY,
        NULL AS EN_NSYL,
        NULL AS EN_XSFWF,
        MAX(CASE WHEN VC_KMDM = '累计单位净值:' THEN EN_SZ END) AS EN_LJJZ,
        NULL AS EN_GPZJZ,
        NULL AS L_ISGZR,
        NULL AS EN_TZGWF,
        NULL AS EN_KFPSY,
        NULL AS EN_FDSY,
        NULL AS EN_30RNHSYL,
        NULL AS EN_JZNZZL,
        NULL AS L_SFQR,
        NULL AS EN_GPCW,
        NULL AS EN_JZLJZZL,
        NULL AS EN_YHCK,
        NULL AS EN_JSF,
        NULL AS EN_JSS,
        NULL AS EN_GLF,
        NULL AS EN_MRJG,
        NULL AS EN_MCJG,
        NULL AS EN_FDFY,
        NULL AS EN_TGF
        FROM hsfa.TTMP_H_GZB
        WHERE
        D_YWRQ &gt;= TO_DATE(#{startDate}, 'YYYY-MM-DD')
        AND D_YWRQ &lt;= TO_DATE(#{today}, 'YYYY-MM-DD')
        AND VC_KMDM IN ('实收资本','今日单位净值:','累计单位净值:','委托资产净值:')
        AND L_ZTBH IN (SELECT DISTINCT L_ZTBH FROM hsfa.TJJXSFL)
        GROUP BY L_ZTBH, D_YWRQ
        UNION ALL
        SELECT
        1 AS flag, -- Placeholder for the flag from the first query
        L_ZTBH,
        D_RQ, -- Cast to CHAR to match the first query's format
        VC_JJDM,
        VC_JJMC,
        EN_JJZJZ,
        EN_JJZFE,
        EN_JJDWJZ,
        EN_JJSY,
        EN_DWJJSY,
        EN_NSYL,
        EN_XSFWF,
        EN_LJJZ,
        EN_GPZJZ,
        L_ISGZR,
        EN_TZGWF,
        EN_KFPSY,
        EN_FDSY,
        EN_30RNHSYL,
        EN_JZNZZL,
        L_SFQR,
        EN_GPCW,
        EN_JZLJZZL,
        EN_YHCK,
        EN_JSF,
        EN_JSS,
        EN_GLF,
        EN_MRJG,
        EN_MCJG,
        EN_FDFY,
        EN_TGF
        FROM
        hsfa.TJJJZ
        <where>
        D_RQ &gt;= TO_DATE(#{startDate}, 'YYYY-MM-DD')
        AND D_RQ &lt;= TO_DATE(#{today}, 'YYYY-MM-DD')
        </where>
        order by L_ZTBH, D_RQ, VC_JJMC
    </select>
    <select id="selectStructuredTableData" resultType="cn.sdata.om.al.audit.entity.Tjjjz">
        select  VC_FJJJDM as vcJjmc, VC_JJDM as vcJjdm from hsfa.TJJXSFL
    </select>

    <select id="getProductCodeMapping" resultType="cn.sdata.om.al.entity.CommonEntity">
        SELECT VC_FJJJDM as "id", VC_JJDM as "name"
        FROM hsfa.TJJXSFL
        WHERE VC_FJJJDM IS NOT NULL AND VC_JJDM IS NOT NULL
    </select>
    <select id="selectMonthEndAndNonStandardInfo"
            resultType="cn.sdata.om.al.audit.entity.MonthEndAndNonStandard">
        <if test="vo.secTypes != null and vo.secTypes.size() > 0">
            SELECT
            TO_CHAR(max(vwg.D_YWRQ), 'YYYY-MM-DD') AS dataDate,
            vwg.L_ZTBH as productId,
            max(vwg.en_cb) as securityCost,
            max(vwg.en_sz) as securityMarketValue,
            max(vwg.EN_HQJZ) as monthEndValuationPrice,
            vwg.VC_SCDM as vcScdm,
            vwz.VC_ZQMC as securityName,
            vwz.VC_ZQDM as securityCode,
            TO_CHAR(vwz.L_ZQLBMX1) as securityType,
            TO_CHAR(max(vwz.D_DQR), 'YYYY-MM-DD') AS dueDate
            FROM HSFA.VJK_WBFK_GZB vwg
            LEFT JOIN HSFA.VJK_WBFK_ZQXXB vwz ON VWG.VC_SCDM = VWZ.VC_SCDM AND VWG.L_SCLB = VWZ.L_SCLB
            WHERE vwg.D_YWRQ = TO_DATE(#{vo.dataDate}, 'YYYY-MM-DD') and vwg.VC_SCDM is not NULL
            <if test="vo.productNames != null and vo.productNames.size() > 0">
                and vwg.L_ZTBH in
                <foreach collection="vo.productNames" open="(" separator="," item="name" close=")">
                    #{name}
                </foreach>
            </if>
            <if test="vo.securityCodes != null and vo.securityCodes.size() > 0">
                and vwz.VC_ZQDM in
                <foreach collection="vo.securityCodes" open="(" separator="," item="code" close=")">
                    #{code}
                </foreach>
            </if>
                and (
            <foreach collection="vo.secTypes" item="type" separator=" OR ">
                (vwz.l_zqlb = #{type.lzqlb} AND vwz.L_ZQLBMX1 = #{type.code})
            </foreach>
            )
            GROUP BY VWG.VC_SCDM,vwg.L_ZTBH,vwg.VC_SCDM, vwz.VC_ZQMC, vwz.VC_ZQDM,TO_CHAR(vwz.L_ZQLBMX1)
        </if>
        <if test="vo.secTypes != null and vo.secTypes.size() > 0 and vo.flag != null and vo.flag != ''">
            union all
        </if>
        <if test="vo.flag != null and vo.flag != ''">
            SELECT
            TO_CHAR(max(vwg.D_YWRQ), 'YYYY-MM-DD') AS dataDate,
            vwc.L_ZTBH as productId,
            sum(vwg.en_cb) as securityCost,
            sum(vwg.en_sz) as securityMarketValue,
            sum(vwg.EN_HQJZ) as monthEndValuationPrice,
            '' as vcScdm,
            MAX(vwc.VC_CKZH) as securityName,
            vwc.VC_CKZH as securityCode,
            '存款' as securityType,
            TO_CHAR(max(vwc.D_DQRQ), 'YYYY-MM-DD')  AS dueDate
            FROM HSFA.VJK_WBFK_CKLSB vwc
            LEFT JOIN HSFA.VJK_WBFK_GZB vwg ON vwg.VC_KMDM = vwc.VC_BJKM  and vwc.L_ZTBH  = vwg.L_ZTBH and  vwc.D_YWRQ = vwg.D_YWRQ
            WHERE vwc.D_YWRQ =  TO_DATE(#{vo.dataDate}, 'YYYY-MM-DD') AND vwg.L_ZTBH IS NOT null
            <if test="vo.productNames != null and vo.productNames.size() > 0">
                and vwc.L_ZTBH in
                <foreach collection="vo.productNames" open="(" separator="," item="name" close=")">
                    #{name}
                </foreach>
            </if>
            GROUP BY vwc.VC_BJKM,vwc.L_ZTBH,vwc.VC_CKZH
        </if>


    </select>
    <select id="selectProdSecurityList" resultType="cn.sdata.om.al.audit.entity.MonthEndAndNonStandard">
        SELECT
            VC_SCDM as vcScdm,
            VC_ZQDM as securityCode,
            L_ZQLB as securityType,
            VC_ZQMC as securityName
            from HSFA.VJK_WBFK_ZQXXB
        <where>
            <if test="securityType != null and securityType != ''">
                and L_ZQLB = #{securityType}
            </if>
            <if test="securityCode != null and securityCode != ''">
                and VC_ZQDM = #{securityCode}
            </if>
        </where>
    </select>

    <!-- 查询估值系统证券持仓数据（用于除权价格检查） -->
    <select id="selectSecurityHoldingForExRightsCheck" resultType="cn.sdata.om.al.audit.dto.SecurityHoldingDto">
        SELECT a.L_ZTBH       AS "productId",
               a.L_ZQNM       AS "securityInternalCode",
               b.L_ZQLB       AS "securityType",
               c.VC_ITEM_NAME AS "securityTypeName",
               b.VC_ZQDM      AS "securityCode",
               a.D_DATE       AS "dataDate",
               b.VC_ZQJC      AS "securityName",
               b.L_SCLB       AS "securityMarket",
               d.VC_BZ        AS "securityMarketName",
               L_ZQCC         AS "securityHolding"

        FROM (SELECT L_ZTBH, L_ZQNM, VC_ZQDM, D_DATE, L_ZQCC
              from hsfa.TACCOUNTZQJC
              where d_date = TO_DATE('2025-06-25', 'YYYY-MM-DD')
                AND L_ZQCC > 0) a,
             HSFA.tzqxx b,
             (SELECT C_LEMMA_ITEM, VC_ITEM_NAME
              FROM HSFA.TDICTIONARY
              WHERE L_DICTIONARY_NO = '10205'
                AND C_LEMMA_ITEM != '!') c,
             (SELECT L_SCLB,MAX(VC_BZ) AS VC_BZ FROM T_O32QS_XJKSCLBYSCLB GROUP BY L_SCLB ORDER BY L_SCLB ) d
        WHERE a.L_ZQNM = b.L_ZQNM
          AND TO_CHAR(b.L_ZQLB) = c.C_LEMMA_ITEM
          AND b.L_SCLB = d.L_SCLB
          AND b.L_ZQLB IN (1, 2, 4)
          AND (
            -- 基金：上海(1)、深圳(2)
            (b.L_ZQLB = 4 AND b.L_SCLB IN (1, 2))
                OR
                -- 股票：上海(1)、深圳(2)、港股通(44,30)、北交所(52)
            (b.L_ZQLB = 1 AND b.L_SCLB IN (1, 2, 44, 30, 52))
                OR
                -- 债券：上海(1)、深圳(2)、银行间(3)、北金所(待确认)
            (b.L_ZQLB = 2 AND b.L_SCLB IN (1, 2, 3))
            )

    </select>

    <!-- 查询估值系统流水（统一使用TFJYGH表） -->
    <select id="selectExRightsFlow" resultType="java.util.Map">
        SELECT
            TO_CHAR(D_RQ, 'YYYY-MM-DD') AS "flowDate",
            VC_ZQDM AS "securityCode",
            L_ZTBH AS "productId",
            L_YWLB AS "businessType",
            L_SCLB AS "transactionQuantity"
        FROM HSFA.TFJYGH
        WHERE VC_ZQDM = #{securityCode}
          AND L_ZTBH = #{productId}
          AND D_RQ = TO_DATE(#{dataDate}, 'YYYY-MM-DD')
    </select>

    <!-- 查询估值系统红股流水 -->
    <select id="selectBonusShareFlow" resultType="java.util.Map">
        SELECT
            TO_CHAR(D_RQ, 'YYYY-MM-DD') AS "flowDate",
            VC_ZQDM AS "securityCode",
            L_ZTBH AS "productId",
            L_YWLB AS "businessType",
            L_SCLB AS "transactionQuantity"
        FROM HSFA.TFJYGH
        WHERE VC_ZQDM = #{securityCode}
          AND L_ZTBH = #{productId}
          AND D_RQ = TO_DATE(#{dataDate}, 'YYYY-MM-DD')
    </select>

    <!-- 查询估值系统红利发放流水 -->
    <select id="selectDividendFlow" resultType="java.util.Map">
        SELECT
            TO_CHAR(D_RQ, 'YYYY-MM-DD') AS "flowDate",
            VC_ZQDM AS "securityCode",
            L_ZTBH AS "productId",
            L_YWLB AS "businessType",
            L_SCLB AS "transactionQuantity"
        FROM HSFA.TFJYGH
        WHERE VC_ZQDM = #{securityCode}
          AND L_ZTBH = #{productId}
          AND D_RQ = TO_DATE(#{dataDate}, 'YYYY-MM-DD')
    </select>
    <select id="seleDataDate" resultType="java.lang.String">
        SELECT *
        FROM (
                 SELECT TO_CHAR(ADD_MONTHS(D_YWRQ, -1), 'YYYY-MM') AS dataDate
                 FROM HSFA.VJK_WBFK_GZB
                 ORDER BY D_YWRQ DESC
             )
        WHERE ROWNUM = 1
    </select>

    <select id="seleDataDateInfo" resultType="java.lang.String">
        SELECT *
        FROM (
                 SELECT TO_CHAR(D_YWRQ, 'YYYYMMDD') AS dataDate
                 FROM HSFA.VJK_WBFK_GZB
                 ORDER BY D_YWRQ DESC
             )
        WHERE ROWNUM = 1
    </select>
    <select id="selectSecInfo" resultType="cn.sdata.om.al.audit.entity.MonthEndAndNonStandard">
        SELECT
        vwz.VC_ZQMC as securityName,
        vwz.VC_ZQDM as securityCode
        FROM HSFA.VJK_WBFK_GZB vwg
        LEFT JOIN HSFA.VJK_WBFK_ZQXXB vwz ON VWG.VC_SCDM = VWZ.VC_SCDM AND VWG.L_SCLB = VWZ.L_SCLB
        WHERE vwg.D_YWRQ = TO_DATE(#{vo.dataDate}, 'YYYY-MM-DD') and vwg.VC_SCDM is not NULL
        <if test="vo.productNames != null and vo.productNames.size() > 0">
            and vwg.L_ZTBH in
            <foreach collection="vo.productNames" open="(" separator="," item="name" close=")">
                #{name}
            </foreach>
        </if>
        and (
        <foreach collection="vo.secTypes" item="type" separator=" OR ">
            (vwz.l_zqlb = #{type.lzqlb} AND vwz.L_ZQLBMX1 = #{type.code})
        </foreach>
        )
        GROUP BY VWG.VC_SCDM,vwg.L_ZTBH,vwg.VC_SCDM, vwz.VC_ZQMC, vwz.VC_ZQDM,TO_CHAR(vwz.L_ZQLBMX1)
    </select>


</mapper>
