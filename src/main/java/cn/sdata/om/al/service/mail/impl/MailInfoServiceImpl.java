package cn.sdata.om.al.service.mail.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.disclosure.MailHandler;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.entity.mail.*;
import cn.sdata.om.al.entity.mail.vo.MailContactsVo;
import cn.sdata.om.al.entity.mail.vo.MailDetailVo;
import cn.sdata.om.al.entity.mail.vo.MailTemplateDetailVo;
import cn.sdata.om.al.enums.MailContactsType;
import cn.sdata.om.al.enums.MailContentHandler;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.mapper.mail.*;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.service.SMBService;
import cn.sdata.om.al.service.SendMailService;
import cn.sdata.om.al.service.mail.MailInfoService;
import cn.sdata.om.al.utils.MailUtil;
import cn.sdata.om.al.utils.OmFileUtil;
import cn.sdata.om.al.utils.StringUtil;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.retry.Retry;
import io.vavr.CheckedRunnable;
import io.vavr.control.Try;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ScheduledExecutorService;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Service
@Slf4j
public class MailInfoServiceImpl implements MailInfoService {

    private MailUtil mailUtil;

    private MailInfoMapper mailInfoMapper;

    private MailTemplateMapper mailTemplateMapper;

    private MailLogMapper mailLogMapper;

    private SMBService smbService;

    private MailAttachmentMapper mailAttachmentMapper;

    private MailContentMapper mailContentMapper;

    private MailContactsMapper mailContactsMapper;

    private SendMailService sendMailService;

    private ApplicationContext applicationContext;

    private ScheduledExecutorService scheduledExecutorService;

    @Value("${file.dir}")
    private String fileDir;

    private RateLimiter rateLimiter;

    private Retry mailRetry;

    @Autowired
    public void setRateLimiter(RateLimiter rateLimiter) {
        this.rateLimiter = rateLimiter;
    }

    @Autowired
    public void setRetry(Retry mailRetry) {
        this.mailRetry = mailRetry;
    }


    @Autowired
    public void setMailUtil(MailUtil mailUtil) {
        this.mailUtil = mailUtil;
    }

    @Autowired
    public void setScheduledExecutorService(ScheduledExecutorService scheduledExecutorService) {
        this.scheduledExecutorService = scheduledExecutorService;
    }

    @Autowired
    public void setMailInfoMapper(MailInfoMapper mailInfoMapper) {
        this.mailInfoMapper = mailInfoMapper;
    }

    @Autowired
    public void setMailLogMapper(MailLogMapper mailLogMapper) {
        this.mailLogMapper = mailLogMapper;
    }

    @Autowired
    public void setSmbService(SMBService smbService) {
        this.smbService = smbService;
    }

    @Autowired
    public void setMailAttachmentMapper(MailAttachmentMapper mailAttachmentMapper) {
        this.mailAttachmentMapper = mailAttachmentMapper;
    }

    @Autowired
    public void setMailContentMapper(MailContentMapper mailContentMapper) {
        this.mailContentMapper = mailContentMapper;
    }

    @Autowired
    public void setMailContactsMapper(MailContactsMapper mailContactsMapper) {
        this.mailContactsMapper = mailContactsMapper;
    }

    @Autowired
    public void setMailTemplateMapper(MailTemplateMapper mailTemplateMapper) {
        this.mailTemplateMapper = mailTemplateMapper;
    }

    @Autowired
    public void setSendMailService(SendMailService sendMailService) {
        this.sendMailService = sendMailService;
    }

    @Autowired
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Override
    public List<SendMailInfo> doSendMailInfo(Cron cron, String dataDate, Set<String> paramProductIds, Map<String, RemoteFileInfo> files) {
        List<SendMailInfo> sendMailInfos = this.composeByConfig(cron, dataDate, paramProductIds, files);
        executeSendMailInfo(sendMailInfos);
        return sendMailInfos;
    }

    @Override
    public List<SendMailInfo> doSendMailInfo(List<SendMailInfo> sendMailInfos) {
        log.info("邮件信息:{}", sendMailInfos);
        executeSendMailInfo(sendMailInfos);
        return sendMailInfos;
    }

    private void executeSendMailInfo(List<SendMailInfo> sendMailInfos) {
        Objects.requireNonNull(sendMailInfos, "发送邮件信息不得为空");
        if (sendMailInfos.isEmpty()) {
            return;
        }
        int numberOfEmails = sendMailInfos.size();
        CountDownLatch completionLatch = new CountDownLatch(numberOfEmails);

        for (SendMailInfo mailInfo : sendMailInfos) {
            Runnable sendTask = () -> {
//                Runnable decoratedTask = RateLimiter.decorateRunnable(rateLimiter, () -> {
//                    log.info("邮件发送当前时间: {}", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
//                    String logId = doSend(mailInfo);
//                    mailInfo.setLogId(logId);
//                    mailInfo.setStatus(MailStatus.SUCCESS.name());
//                });
//                try {
//                    decoratedTask.run();
//
//                Retry mailRetry = Retry.of("mailRetry", retryConfig);
                try {
                    // 用 Retry 包装 doSend
                    Runnable retryable = Retry.decorateRunnable(mailRetry, () -> {
                        log.info("邮件发送当前时间: {}", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                        String logId = doSend(mailInfo);
                        mailInfo.setLogId(logId);
                        mailInfo.setStatus(MailStatus.SUCCESS.name());
                    });

                    // 再用 RateLimiter 包装，确保发信频率受控
                    Runnable decoratedTask = RateLimiter.decorateRunnable(rateLimiter, retryable);

                    // 执行任务
                    decoratedTask.run();
                } catch (Exception e) {
                    log.error("邮件发送异常: {}", e.getMessage(), e);
                    mailInfo.setStatus(MailStatus.FAILED.name());
                } finally {
                    completionLatch.countDown();
                }
            };
            scheduledExecutorService.submit(sendTask);
        }

        try {
            completionLatch.await();
            log.info("所有 {} 个邮件发送任务已完成", numberOfEmails);
        } catch (InterruptedException e) {
            log.error("邮件发送过程中中断: {}", e.getMessage());
            Thread.currentThread().interrupt();
        }
    }

    @Override
    public List<SendMailInfo> composeByConfig(Cron cron, String dataDate, Set<String> paramProductIds, Map<String, RemoteFileInfo> files) {
        Objects.requireNonNull(cron, "任务信息不得为空");
        String contactType = cron.getContactType();
        Objects.requireNonNull(contactType, "收件人类型不得为空");
        MailContactsType mailContactsType = MailContactsType.valueOf(contactType);
        Objects.requireNonNull(mailContactsType, "类型不是预设类型");
        String jobId = cron.getJobId();
        //得到任务相关的所有发送邮件配置, 全部都需要发送
        List<MailContactsVo> contactsInfos = mailContactsMapper.getInfoByTaskId(jobId);
        List<SendMailInfo> sendMailInfos = new ArrayList<>();
        log.info("查询的通讯录信息为:{}", contactsInfos);
        for (MailContactsVo contactsInfo : contactsInfos) {
            String typeValues = contactsInfo.getTypeValues();
            List<String> values = StringUtil.splitStr(typeValues, ",");
            values = sendMailService.dealAll(values);
            String spiltType = contactsInfo.getType();
            String role = contactsInfo.getCategory();
            String templateId = contactsInfo.getTemplateId();
            Integer immediateSend = contactsInfo.getImmediateSend();
            MailTemplateDetailVo mailTemplate = mailTemplateMapper.getById(templateId);
            log.info("发送邮件的mailContactsType = {}", mailContactsType);
            switch (mailContactsType) {
                case CUSTODIAN_BANK:
                    List<SendMailInfo> custodianBankMail = sendMailService.getCustodianBankContact(values, spiltType, role, paramProductIds, immediateSend);
                    log.info("托管行信息:{}", custodianBankMail);
                    custodianBankMail.forEach(sendMailInfo -> {
                        Map<String, Object> extraData = cron.getExtraData();
                        sendMailInfo.setExtraData(extraData);
                        SendMailInfo result = dealHandler(sendMailInfo, dataDate, files, mailTemplate);
                        sendMailInfos.add(result);
                    });
                    break;
                case INVESTOR:
                    List<SendMailInfo> investorContactMail = sendMailService.getInvestorContact(values, spiltType, paramProductIds, cron.getDisclosureHandler(), BaseConstant.INVESTOR_TYPE, immediateSend);
                    log.info("投资人信息:{}", investorContactMail);
                    investorContactMail.forEach(sendMailInfo -> {
                        SendMailInfo result = dealHandler(sendMailInfo, dataDate, files, mailTemplate);
                        result.setContactType(BaseConstant.INVESTOR_TYPE);
                        sendMailInfos.add(result);
                    });
                    break;
                case THREE_PARTY_ORGAN:
                    List<SendMailInfo> thirdContactMail = sendMailService.getInvestorContact(values, spiltType, paramProductIds, cron.getDisclosureHandler(), BaseConstant.INVESTOR_THIRD, immediateSend);
                    log.info("第三方信息:{}", thirdContactMail);
                    thirdContactMail.forEach(sendMailInfo -> {
                        SendMailInfo result = dealHandler(sendMailInfo, dataDate, files, mailTemplate);
                        result.setContactType(BaseConstant.INVESTOR_TYPE);
                        sendMailInfos.add(result);
                    });
                    break;
                case OTHER:
                    List<SendMailInfo> otherContactMail = sendMailService.getOtherContact(values);
                    String specifiedHandler = cron.getSpecifiedHandler();
                    otherContactMail.forEach(sendMailInfo -> {
                        if (specifiedHandler != null) {
                            sendMailInfo.setHandler(specifiedHandler);
                        }
                        Map<String, Object> extraData = cron.getExtraData();
                        sendMailInfo.setExtraData(extraData);
                        sendMailInfo.setProductIds(new ArrayList<>(paramProductIds));
                        SendMailInfo result = dealHandler(sendMailInfo, dataDate, files, mailTemplate);
                        if (result != null) {
                            sendMailInfos.add(result);
                        }
                    });
                    break;
            }
        }
        return sendMailInfos;
    }

    private SendMailInfo dealHandler(SendMailInfo sendMailInfo, String dataDate, Map<String, RemoteFileInfo> files, MailTemplateDetailVo mailTemplate) {
        Objects.requireNonNull(sendMailInfo, "发送邮件信息不得为空");
        Objects.requireNonNull(mailTemplate, "发送邮件信息不得为空");
        String handlerName = sendMailInfo.getHandler();
        MailContentHandler mailContentHandler = MailContentHandler.valueOf(handlerName);
        Objects.requireNonNull(mailContentHandler, "处理器不存在");
        String handler = mailContentHandler.getHandler();
        Map<String, MailHandler> beansOfType = applicationContext.getBeansOfType(MailHandler.class);
        MailHandler mailHandler = beansOfType.get(handler);
        sendMailInfo.setTemplateId(mailTemplate.getTemplateId());
        sendMailInfo.setDataDate(dataDate);
        int compressAttachmentIs = 0;
        try {
            String extStr = mailTemplate.getMailTemplateExtStr();
            MailTemplateExt mailTemplateExt = JSONUtil.toBean(extStr, MailTemplateExt.class);
            compressAttachmentIs = mailTemplateExt.getCompressAttachmentIs();
        } catch (Exception e) {
            log.error("解析邮件模板扩展字段失败: {}", e.getMessage(), e);
        }
        sendMailInfo.setCompressAttachmentIs(compressAttachmentIs);
        return mailHandler.execute(sendMailInfo, dataDate, files, mailTemplate);
    }

    /**
     * 将远程文件中的文件保存在本地
     *
     * @param mailAttachments 文件信息
     */
    private void saveAsFileToLocal(List<MailAttachment> mailAttachments) {
        for (MailAttachment mailAttachment : mailAttachments) {
            byte[] content = mailAttachment.getContent();
            String filePath = fileDir + mailAttachment.getFileName();
            log.info("----将远程文件存入到本地的文件地址是:{}", filePath);
            File destFile = FileUtil.writeBytes(content, filePath);
            // 覆盖原先远程地址改为本地
            if (ObjectUtil.isNotNull(destFile)) {
                mailAttachment.setFilePath(destFile.getAbsolutePath());
            }
        }
    }

    @Override
    public File downloadAttachments(String mailId, String fileId) {
        if (StringUtils.isNotBlank(fileId)) {
            MailAttachment mailAttachment = mailAttachmentMapper.selectMailAttachmentsByFileId(fileId);
            if (ObjectUtil.isNotNull(mailAttachment)) {
                return new File(mailAttachment.getFilePath());
            }
        }
        MailDetailVo mailDetailVo = mailInfoMapper.getById(mailId);
        if (ObjectUtil.isNull(mailDetailVo)) {
            // 尝试从收件信息中获取
            MailContent mailContent = mailContentMapper.getById(mailId);
            if (ObjectUtil.isNull(mailContent)) {
                BusinessException.throwException("发送和收件邮件都不存在");
            }
            mailDetailVo = new MailDetailVo();
            mailDetailVo.setTitle(mailContent.getSubject());
        }
        List<MailAttachment> mailAttachments = mailAttachmentMapper.getMailAttachments(mailId);
        if (CollectionUtil.isNotEmpty(mailAttachments)) {
            if (mailAttachments.size() > 1) {
                // 合并为zip文件
                String title = mailDetailVo.getTitle();
                File zipFile = FileUtil.createTempFile(title + "的附件", ".zip", true);
                List<File> fileList = new ArrayList<>();
                for (MailAttachment mailAttachment : mailAttachments) {
                    String filePath = mailAttachment.getFilePath();
                    try {
                        File file = new File(filePath);
                        fileList.add(file);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }
                if (CollectionUtil.isNotEmpty(fileList)) {
                    OmFileUtil.zipFiles(fileList, zipFile);
                    return zipFile;
                }
            } else {
                MailAttachment mailAttachment = mailAttachments.get(0);
                try {
                    return new File(mailAttachment.getFilePath());
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return null;
    }

    @Override
    public boolean testSendMail(String url, String filePath) {
        List<String> recipientList = CollectionUtil.toList(url.split(";"));
        List<String> ccToList = CollectionUtil.toList(url.split(";"));
        List<MailAttachment> mailAttachments;
        String content = "测试邮件正文";
        try {
            // 替换标题
            String title = "测试标题";
            log.info("---替换后的标题为:{}", title);
            // 从共享文件夹中获取附件 并根据账套获取对应附件
            mailAttachments = getAttachments(CollectionUtil.newArrayList(filePath), new ArrayList<>());
            // 将远程文件中的文件保存在本地
            if (CollectionUtil.isNotEmpty(mailAttachments)) {
                saveAsFileToLocal(mailAttachments);
            }
            // 发送邮件
            mailUtil.sendEmailFileFromShareFolder(recipientList, ccToList, mailAttachments, title, content);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return false;
    }

    /**
     * 发送邮件
     *
     * @param sendMailInfo 发送内容
     * @return 邮件id
     */
    private String doSend(SendMailInfo sendMailInfo) {
        Objects.requireNonNull(sendMailInfo, "发送邮件信息不得为空");
        String sendMailId = IdUtil.getSnowflakeNextIdStr();
        MailInfo mailInfo = new MailInfo();
        Exception exception = null;
        List<String> recipients = sendMailInfo.getRecipient();
        String subject = sendMailInfo.getSubject();
        List<String> cc = sendMailInfo.getCc();
        String sender = "调度平台";
        Date sendTime = new Date();
        List<MailAttachment> mailAttachments;
        int compressAttachmentIs = sendMailInfo.getCompressAttachmentIs();
        try {
            // 从共享文件夹中获取附件 并根据账套获取对应附件
            mailAttachments = getAttachments(sendMailInfo.getAttachment(), compressAttachmentIs);
            // 发送邮件
            mailUtil.sendEmailFileFromShareFolder(recipients, cc, mailAttachments, subject, sendMailInfo.getContent());
            return sendMailId;
        } catch (Exception e) {
            log.error("MailInfoServiceImpl_doSend_error:{},{}", e, e.getMessage());
            exception = e;
            throw new RuntimeException("邮件发送失败", e);
        } finally {
            log.info("MailInfoServiceImpl_finally_start");
            try {
                MailLog mailLog = new MailLog();
                if (exception != null) {
                    mailLog.setSendStatus(1);
                    mailLog.setErrorMsg(exception.getMessage());
                    mailInfo.setSendStatus(1);
                }
                // 记录发送邮件信息表
                mailInfo.setId(sendMailId);
                mailInfo.setSender(sender);
                mailInfo.setTitle(subject);
                mailInfo.setTemplateId(sendMailInfo.getTemplateId());
                mailInfo.setRecipient(String.join(",", sendMailInfo.getRecipient()));
                mailInfo.setCcTo(String.join(", ", sendMailInfo.getCc()));
                mailInfo.setContent(sendMailInfo.getContent());

                log.info("MailInfoServiceImpl_finally_mailInfo:{}", mailInfo);

                mailInfoMapper.save(mailInfo);
                // 保存附件与邮件关系
                List<String> attachmentPaths = sendMailInfo.getAttachmentPaths();
                if (CollectionUtil.isNotEmpty(attachmentPaths)) {
                    List<MailAttachment> attachments = new ArrayList<>();
                    for (String path : attachmentPaths) {
                        MailAttachment mailAttachment = new MailAttachment();
                        mailAttachment.setId(IdUtil.getSnowflakeNextIdStr());
                        mailAttachment.setType(0);
                        mailAttachment.setFileName(FileUtil.getName(path));
                        mailAttachment.setFilePath(path);
                        mailAttachment.setMailId(mailInfo.getId());
                        attachments.add(mailAttachment);
                    }
                    if (CollUtil.isNotEmpty(attachments)) {
                        mailAttachmentMapper.batchSave(attachments);
                    }
                }

                // 记录发送邮件日志
                mailLog.setId(IdUtil.getSnowflakeNextIdStr());
                mailLog.setMailId(mailInfo.getId());
                mailLog.setSender(sender);
                mailLog.setRecipient(String.join(",", recipients));
                mailLog.setTitle(subject);
                mailLog.setCcTo(String.join(",", cc));
                mailLog.setSendTime(sendTime);

                log.info("MailInfoServiceImpl_finally_mailLog:{}", mailLog.toString());

                mailLogMapper.save(mailLog);

                //反向记录邮件id
                sendMailInfo.setMailId(mailInfo.getId());
                //反向记录邮件发送日志id
                sendMailInfo.setMailSendLogId(mailLog.getId());
                log.info("MailInfoServiceImpl_finally_end");
            } catch (Exception e) {
                log.info("MailInfoServiceImpl_finally_error:{},{}", e, e.getMessage());
                throw new RuntimeException("邮件发送处理失败", e);
            }
        }
    }


    private String doSendV2(SendMailInfo sendMailInfo) {
        Objects.requireNonNull(sendMailInfo, "发送邮件信息不得为空");

        String sendMailId = IdUtil.getSnowflakeNextIdStr();
        MailInfo mailInfo = new MailInfo();
        MailLog mailLog = new MailLog();

        Exception exception = null;
        // 最大重试次数
        int maxRetry = 3;
        int attempt = 0;
        boolean success = false;

        List<String> recipients = sendMailInfo.getRecipient();
        String subject = sendMailInfo.getSubject();
        List<String> cc = sendMailInfo.getCc();
        String sender = "调度平台";
        Date sendTime = new Date();
        int compressAttachmentIs = sendMailInfo.getCompressAttachmentIs();

        try {
            List<MailAttachment> mailAttachments =
                    getAttachments(sendMailInfo.getAttachment(), compressAttachmentIs);

            while (attempt < maxRetry && !success) {
                attempt++;
                try {
                    log.info("尝试第 {} 次发送邮件，收件人：{}", attempt, recipients);
                    mailUtil.sendEmailFileFromShareFolder(
                            recipients,
                            cc,
                            mailAttachments,
                            subject,
                            sendMailInfo.getContent()
                    );
                    success = true;
                    log.info("邮件发送成功，第 {} 次尝试", attempt);
                } catch (Exception e) {
                    exception = e;
                    log.error("第 {} 次邮件发送失败: {}", attempt, e.getMessage(), e);
                    if (attempt < maxRetry) {
                        try {
                            // 简单退避：第1次等2秒，第2次等4秒...
                            Thread.sleep(2000L * attempt);
                        } catch (InterruptedException ignored) {}
                    }
                }
            }

            if (!success) {
                log.error("邮件发送失败，已重试 {} 次", maxRetry, exception);
            }

            return sendMailId;
        } catch (Exception e) {
            exception = e;
            throw new RuntimeException("邮件发送失败", e);
        } finally {
            log.info("MailInfoServiceImpl_finally_start");
            try {
                // 邮件信息入库
                if (exception != null) {
                    mailInfo.setSendStatus(1);
                    mailLog.setSendStatus(1);
                    mailLog.setErrorMsg(exception.getMessage());
                } else {
                    mailInfo.setSendStatus(0);
                    mailLog.setSendStatus(0);
                }

                mailInfo.setId(sendMailId);
                mailInfo.setSender(sender);
                mailInfo.setTitle(subject);
                mailInfo.setTemplateId(sendMailInfo.getTemplateId());
                mailInfo.setRecipient(String.join(",", recipients));
                mailInfo.setCcTo(String.join(",", cc));
                mailInfo.setContent(sendMailInfo.getContent());

                log.info("MailInfoServiceImpl_finally_mailInfo:{}", mailInfo);
                mailInfoMapper.save(mailInfo);

                // 附件入库
                List<String> attachmentPaths = sendMailInfo.getAttachmentPaths();
                if (CollectionUtil.isNotEmpty(attachmentPaths)) {
                    List<MailAttachment> attachments = new ArrayList<>();
                    for (String path : attachmentPaths) {
                        MailAttachment mailAttachment = new MailAttachment();
                        mailAttachment.setId(IdUtil.getSnowflakeNextIdStr());
                        mailAttachment.setType(0);
                        mailAttachment.setFileName(FileUtil.getName(path));
                        mailAttachment.setFilePath(path);
                        mailAttachment.setMailId(mailInfo.getId());
                        attachments.add(mailAttachment);
                    }
                    if (CollUtil.isNotEmpty(attachments)) {
                        mailAttachmentMapper.batchSave(attachments);
                    }
                }

                // 发送日志入库
                mailLog.setId(IdUtil.getSnowflakeNextIdStr());
                mailLog.setMailId(mailInfo.getId());
                mailLog.setSender(sender);
                mailLog.setRecipient(String.join(",", recipients));
                mailLog.setTitle(subject);
                mailLog.setCcTo(String.join(",", cc));
                mailLog.setSendTime(sendTime);

                log.info("MailInfoServiceImpl_finally_mailLog:{}", mailLog);
                mailLogMapper.save(mailLog);

                // 回写 sendMailInfo 的 ID
                sendMailInfo.setMailId(mailInfo.getId());
                sendMailInfo.setMailSendLogId(mailLog.getId());

                log.info("MailInfoServiceImpl_finally_end");
            } catch (Exception e) {
                log.error("MailInfoServiceImpl_finally_error:{},{}", e, e.getMessage());
                throw new RuntimeException("邮件发送处理失败", e);
            }
        }
    }


    private List<MailAttachment> getAttachments(@NonNull Map<String, byte[]> attachmentContent, int compressAttachmentIs) {
        List<MailAttachment> mailAttachments = new ArrayList<>();
        if (compressAttachmentIs == 1) {
            MailAttachment mailAttachment = compressAttachmentsToZip(attachmentContent);
            if (mailAttachment != null) {
                mailAttachments.add(mailAttachment);
            }
        } else {
            for (Map.Entry<String, byte[]> next : attachmentContent.entrySet()) {
                String fileName = next.getKey();
                byte[] value = next.getValue();
                MailAttachment mailAttachment = new MailAttachment();
                mailAttachment.setInputStream(new ByteArrayInputStream(value));
                mailAttachment.setFileName(fileName);
                mailAttachment.setContent(value);
                mailAttachments.add(mailAttachment);
            }
        }
        return mailAttachments;
    }

    /**
     * 将多个附件压缩为单个ZIP附件
     *
     * @param attachmentContent 附件内容映射
     * @return 包含ZIP文件的邮件附件对象
     */
    private MailAttachment compressAttachmentsToZip(@NotNull Map<String, byte[]> attachmentContent) {
        if (attachmentContent.isEmpty()) {
            return null;
        }
        try {
            ByteArrayOutputStream outputStream = getByteArrayOutputStream(attachmentContent);
            // 创建ZIP附件
            byte[] zipContent = outputStream.toByteArray();
            MailAttachment zipAttachment = new MailAttachment();
            zipAttachment.setInputStream(new ByteArrayInputStream(zipContent));
            zipAttachment.setFileName("附件.zip");
            zipAttachment.setContent(zipContent);
            return zipAttachment;
        } catch (IOException e) {
            log.error("创建ZIP附件失败: {}", e.getMessage(), e);
            return null;
        }
    }

    private @NonNull ByteArrayOutputStream getByteArrayOutputStream(@NotNull Map<String, byte[]> attachmentContent) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try (ZipOutputStream zipOut = new ZipOutputStream(outputStream)) {
            // 将所有附件添加到ZIP文件中
            for (Map.Entry<String, byte[]> entry : attachmentContent.entrySet()) {
                String fileName = entry.getKey();
                byte[] fileContent = entry.getValue();
                ZipEntry zipEntry = new ZipEntry(fileName);
                zipOut.putNextEntry(zipEntry);
                zipOut.write(fileContent);
                zipOut.closeEntry();
            }
        }
        return outputStream;
    }

    /**
     * 从共享文件中获取附件
     *
     * @param filePaths      文件路径
     * @param commonEntities 账套信息
     * @return 文件信息
     */
    private List<MailAttachment> getAttachments(List<String> filePaths, List<CommonEntity> commonEntities) {
        List<MailAttachment> mailAttachments = new ArrayList<>();
        try {
            if (CollectionUtil.isNotEmpty(filePaths)) {
                boolean needFilter = CollectionUtil.isNotEmpty(commonEntities);
                // 如果没有账套则默认取全部文件当作附件
                List<String> accountSetNames = commonEntities.stream().map(CommonEntity::getName).collect(Collectors.toList());
                String path = filePaths.get(0);
                String extName = FileUtil.extName(path);
                if (StringUtils.isNotBlank(extName)) {
                    log.info("---发送的路径是文件---");
                    // 传入的路径代表文件
                    return generateMailAttachments(filePaths, accountSetNames, needFilter);
                }
                log.info("---发送的路径是目录---");
                for (String fileDirPath : filePaths) {
                    log.info("共享文件夹目录为:{}", fileDirPath);
                    List<String> fileDirs = smbService.listRelativeDir(fileDirPath);
                    log.info("共享文件夹目录对应的文件路径为:{}", fileDirs);
                    // 通过比对账套字段后,需要下载的文件
                    return generateMailAttachments(fileDirs, accountSetNames, needFilter);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return mailAttachments;
    }

    /**
     * 生成文件对象
     *
     * @param fileDirs        文件或目录地址
     * @param accountSetNames 账套名称
     * @param needFilter      是否需要过滤
     * @return 文件对象
     */
    private List<MailAttachment> generateMailAttachments(List<String> fileDirs, List<String> accountSetNames, boolean needFilter) {
        List<MailAttachment> mailAttachments = new ArrayList<>();
        List<String> downloadFileDirs = new ArrayList<>();
        for (String fileDir : fileDirs) {
            log.info("***************文件路径是***************:{}", fileDir);
            if (!needFilter) {
                downloadFileDirs.add(fileDir);
            } else {
                String fileName = FileUtil.getName(fileDir);
                fileName = fileName.replaceAll("\\\\", "/");
                log.info("*********文件名为:{}", fileName);
                if (StringUtils.isNotBlank(fileName)) {
                    for (String accountSetName : accountSetNames) {
                        log.info("*******需要比对的文件名称是:{}, 账套名称为:{}", fileName, accountSetName);
                        if (StringUtils.isNotBlank(accountSetName)) {
                            if (fileName.contains(accountSetName)) {
                                downloadFileDirs.add(fileDir);
                                break;
                            }
                        }
                    }
                }
            }
        }
        if (downloadFileDirs.isEmpty()) {
            downloadFileDirs.addAll(fileDirs);
        }
        if (CollectionUtil.isNotEmpty(downloadFileDirs)) {
            log.info("----需要下载的文件地址为:{}", downloadFileDirs);
            // 通过共享文件夹下载文件
            for (String downloadDir : downloadFileDirs) {
                MailAttachment mailAttachment = new MailAttachment();
                ShareDownloadFile shareDownloadFile = smbService.downloadReturnFile(downloadDir);
                mailAttachment.setInputStream(shareDownloadFile.getInputStream());
                mailAttachment.setFileName(shareDownloadFile.getFileName());
                mailAttachment.setFilePath(shareDownloadFile.getFilePath());
                mailAttachment.setContent(shareDownloadFile.getContent());
                mailAttachments.add(mailAttachment);
            }
        }
        return mailAttachments;
    }

}
