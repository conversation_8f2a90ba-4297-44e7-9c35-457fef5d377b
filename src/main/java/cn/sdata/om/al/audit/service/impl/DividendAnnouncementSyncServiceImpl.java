package cn.sdata.om.al.audit.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.sdata.om.al.audit.entity.DividendAnnouncementData;
import cn.sdata.om.al.audit.mapper.DividendAnnouncementDataMapper;
import cn.sdata.om.al.audit.mapper.JuyuanDataMapper;
import cn.sdata.om.al.audit.service.DividendAnnouncementSyncService;
import cn.sdata.om.al.service.BankReconciliationService;
import cn.sdata.om.al.result.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分红公告数据同步服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DividendAnnouncementSyncServiceImpl implements DividendAnnouncementSyncService {

    private final JuyuanDataMapper juyuanDataMapper;
    private final DividendAnnouncementDataMapper dividendAnnouncementDataMapper;
    private final BankReconciliationService bankReconciliationService;

    @Override
    public R<String> syncJuyuanDividendData(String syncDate) {
        try {
            log.info("开始同步聚源分红公告数据，同步日期：{}", syncDate);

            // 1. 从聚源数据库获取所有类型的分红公告数据
            List<DividendAnnouncementData> allData = new ArrayList<>();
            
            // 获取股票分红数据
            List<DividendAnnouncementData> stockData = fetchStockDividendData();
            if (CollectionUtil.isNotEmpty(stockData)) {
                allData.addAll(stockData);
                log.info("获取股票分红公告数据：{} 条", stockData.size());
            }

            // 获取港股分红数据
            List<DividendAnnouncementData> hkStockData = fetchHkStockDividendData();
            if (CollectionUtil.isNotEmpty(hkStockData)) {
                allData.addAll(hkStockData);
                log.info("获取港股分红公告数据：{} 条", hkStockData.size());
            }

            // 获取债券分红数据（使用高效查询）
            List<DividendAnnouncementData> bondData = fetchBondDividendData();
            if (CollectionUtil.isNotEmpty(bondData)) {
                allData.addAll(bondData);
                log.info("获取债券分红公告数据：{} 条", bondData.size());
            }

            // 获取基金分红数据
            List<DividendAnnouncementData> fundData = fetchFundDividendData();
            if (CollectionUtil.isNotEmpty(fundData)) {
                allData.addAll(fundData);
                log.info("获取基金分红公告数据：{} 条", fundData.size());
            }

            // 获取股票指标分红数据
            List<DividendAnnouncementData> stockIndicatorData = fetchStockIndicatorDividendData();
            if (CollectionUtil.isNotEmpty(stockIndicatorData)) {
                allData.addAll(stockIndicatorData);
                log.info("获取股票指标分红公告数据：{} 条", stockIndicatorData.size());
            }

            if (CollectionUtil.isEmpty(allData)) {
                log.info("未获取到聚源分红公告数据");
                return R.ok("未获取到分红公告数据");
            }



            // 2. 分批过滤和保存数据（避免内存溢出和CPU 100%）
            log.info("开始分批过滤和保存数据，总数据量：{} 条", allData.size());
            int totalNewRecords = processBatchData(allData, syncDate);

            log.info("聚源分红公告数据同步完成，成功入库 {} 条记录", totalNewRecords);
            return R.ok("同步完成，新增记录数：" + totalNewRecords);

        } catch (Exception e) {
            log.error("同步聚源分红公告数据失败", e);
            return R.failed("同步失败：" + e.getMessage());
        }
    }

    @Override
    public List<DividendAnnouncementData> fetchStockDividendData() {
        try {
            return juyuanDataMapper.queryStockDividendData();
        } catch (Exception e) {
            log.error("获取股票分红公告数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<DividendAnnouncementData> fetchHkStockDividendData() {
        try {
            return juyuanDataMapper.queryHkStockDividendData();
        } catch (Exception e) {
            log.error("获取港股分红公告数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<DividendAnnouncementData> fetchBondDividendData() {
        try {
            return juyuanDataMapper.queryBondDividendData();
        } catch (Exception e) {
            log.error("获取债券分红公告数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 分批获取债券分红公告数据
     */
    public List<DividendAnnouncementData> fetchBondDividendDataWithPaging(int offset, int limit) {
        try {
            return juyuanDataMapper.queryBondDividendDataWithPaging(offset, limit);
        } catch (Exception e) {
            log.error("分页获取债券分红公告数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取债券分红公告数据总数
     */
    public long getBondDividendDataCount() {
        try {
            return juyuanDataMapper.countBondDividendData();
        } catch (Exception e) {
            log.error("获取债券分红公告数据总数失败", e);
            return 0;
        }
    }

    @Override
    public List<DividendAnnouncementData> fetchStockIndicatorDividendData() {
        try {
            return juyuanDataMapper.queryStockIndicatorDividendData();
        } catch (Exception e) {
            log.error("获取股票指标分红公告数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public R<String> syncAllBondDividendData() {
        log.info("开始分批同步所有债券聚源数据");

        try {
            // 1. 获取总数据量
            long totalCount = getBondDividendDataCount();
            if (totalCount == 0) {
                log.warn("未获取到债券分红公告数据");
                return R.ok("未获取到数据");
            }

            log.info("聚源债券分红公告数据总量：{} 条", totalCount);

            // 2. 分批处理参数
            int batchSize = 10000; // 每批处理1万条
            int totalBatches = (int) Math.ceil((double) totalCount / batchSize);
            int totalNewRecords = 0;

            log.info("将分 {} 批处理，每批 {} 条记录", totalBatches, batchSize);

            // 3. 分批同步数据
            for (int batch = 0; batch < totalBatches; batch++) {
                int offset = batch * batchSize;
                log.info("开始处理第 {}/{} 批，偏移量：{}", batch + 1, totalBatches, offset);

                // 获取当前批次数据
                List<DividendAnnouncementData> batchData = fetchBondDividendDataWithPaging(offset, batchSize);
                if (batchData.isEmpty()) {
                    log.warn("第 {} 批数据为空，跳过", batch + 1);
                    continue;
                }

                // 过滤新数据
                List<DividendAnnouncementData> newData = filterNewData(batchData);
                if (newData.isEmpty()) {
                    log.info("第 {} 批没有新数据需要同步", batch + 1);
                    continue;
                }

                // 设置同步日期和时间戳
                LocalDateTime now = LocalDateTime.now();
                LocalDate syncLocalDate = LocalDate.now();
                newData.forEach(data -> {
                    data.setSyncDate(syncLocalDate);
                    data.setCreateTime(now);
                    data.setUpdateTime(now);
                });

                // 批量保存新数据
                ((DividendAnnouncementSyncServiceImpl) AopContext.currentProxy()).saveDividendDataInTransaction(newData);
                totalNewRecords += newData.size();

                log.info("第 {}/{} 批处理完成，本批新增 {} 条记录，累计新增 {} 条记录",
                        batch + 1, totalBatches, newData.size(), totalNewRecords);

                // 添加短暂延迟，避免对数据库造成过大压力
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("分批处理被中断");
                    break;
                }
            }

            log.info("所有债券聚源分红公告数据同步完成，总共新增 {} 条记录", totalNewRecords);
            return R.ok("所有债券数据同步成功，新增 " + totalNewRecords + " 条记录");

        } catch (Exception e) {
            log.error("所有债券聚源数据同步失败", e);
            return R.failed("所有债券数据同步失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有债券分红公告数据
     */
    public List<DividendAnnouncementData> fetchAllBondDividendData() {
        try {
            return juyuanDataMapper.queryAllBondDividendData();
        } catch (Exception e) {
            log.error("获取所有债券分红公告数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<DividendAnnouncementData> fetchFundDividendData() {
        try {
            return juyuanDataMapper.queryFundDividendData();
        } catch (Exception e) {
            log.error("获取基金分红公告数据失败", e);
            return new ArrayList<>();
        }
    }



    @Override
    public List<DividendAnnouncementData> filterNewData(List<DividendAnnouncementData> allData) {
        if (CollectionUtil.isEmpty(allData)) {
            return new ArrayList<>();
        }

        try {
            // 提取所有聚源ID
            List<String> juyuanIds = allData.stream()
                    .map(DividendAnnouncementData::getJuyuanId)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(juyuanIds)) {
                return new ArrayList<>();
            }

            // 分批查询已存在的ID（避免IN子句过长）
            List<String> existingIds = new ArrayList<>();
            int batchSize = 1000;
            for (int i = 0; i < juyuanIds.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, juyuanIds.size());
                List<String> batchIds = juyuanIds.subList(i, endIndex);
                List<String> batchExistingIds = dividendAnnouncementDataMapper.queryExistingJuyuanIds(batchIds);
                if (CollectionUtil.isNotEmpty(batchExistingIds)) {
                    existingIds.addAll(batchExistingIds);
                }
            }

            // 过滤出新数据
            return allData.stream()
                    .filter(data -> !existingIds.contains(data.getJuyuanId()))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("过滤新数据时发生异常", e);
            // 如果过滤失败，返回空列表避免重复数据
            return new ArrayList<>();
        }
    }

    /**
     * 在事务中保存分红公告数据
     *
     * @param dataList 数据列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveDividendDataInTransaction(List<DividendAnnouncementData> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            log.warn("数据列表为空，跳过保存");
            return;
        }

        log.info("开始批量保存分红公告数据，数据量：{} 条", dataList.size());

        // 对银行间证券代码进行转换处理
        processInterbankSecurityCodes(dataList);

        // 如果数据量较大，分批保存以避免SQL语句过长
        int batchSize = 5000; // 每批保存5000条
        if (dataList.size() <= batchSize) {
            // 数据量不大，直接保存
            dividendAnnouncementDataMapper.batchInsert(dataList);
        } else {
            // 数据量较大，分批保存
            int totalBatches = (int) Math.ceil((double) dataList.size() / batchSize);
            log.info("数据量较大，将分 {} 批保存", totalBatches);

            for (int i = 0; i < totalBatches; i++) {
                int fromIndex = i * batchSize;
                int toIndex = Math.min((i + 1) * batchSize, dataList.size());
                List<DividendAnnouncementData> subList = dataList.subList(fromIndex, toIndex);

                log.debug("保存第 {}/{} 批数据，数量：{} 条", i + 1, totalBatches, subList.size());
                dividendAnnouncementDataMapper.batchInsert(subList);
            }
        }

        log.info("批量保存分红公告数据完成");
    }



    /**
     * 分批处理数据，避免内存溢出和CPU 100%
     */
    private int processBatchData(List<DividendAnnouncementData> allData, String syncDate) {
        int totalNewRecords = 0;
        int batchSize = 5000; // 每批处理5000条
        int totalBatches = (int) Math.ceil((double) allData.size() / batchSize);

        LocalDateTime now = LocalDateTime.now();
        LocalDate syncLocalDate = LocalDate.parse(syncDate);

        for (int i = 0; i < totalBatches; i++) {
            int fromIndex = i * batchSize;
            int toIndex = Math.min((i + 1) * batchSize, allData.size());
            List<DividendAnnouncementData> batchData = allData.subList(fromIndex, toIndex);

            log.info("处理第 {}/{} 批数据，数量：{} 条", i + 1, totalBatches, batchData.size());

            try {
                // 过滤新数据
                List<DividendAnnouncementData> newData = filterNewData(batchData);
                if (CollectionUtil.isEmpty(newData)) {
                    log.info("第 {}/{} 批没有新数据", i + 1, totalBatches);
                    continue;
                }

                // 设置同步日期和时间戳
                newData.forEach(data -> {
                    data.setSyncDate(syncLocalDate);
                    data.setCreateTime(now);
                    data.setUpdateTime(now);
                });

                // 批量保存
                ((DividendAnnouncementSyncServiceImpl) AopContext.currentProxy()).saveDividendDataInTransaction(newData);
                totalNewRecords += newData.size();

                log.info("第 {}/{} 批处理完成，新增：{} 条，累计：{} 条",
                        i + 1, totalBatches, newData.size(), totalNewRecords);

                // 释放内存，避免OOM
                newData.clear();

            } catch (Exception e) {
                log.error("第 {}/{} 批处理失败", i + 1, totalBatches, e);
                // 继续处理下一批，不中断整个流程
            }

            // 添加延迟，避免数据库压力过大
            if (i < totalBatches - 1) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("分批处理被中断");
                    break;
                }
            }
        }

        return totalNewRecords;
    }

    /**
     * 处理银行间证券代码转换
     * 对于银行间市场（securityMarket = "89"）的证券，将原始代码转换为估值系统格式
     *
     * @param dataList 分红数据列表
     */
    private void processInterbankSecurityCodes(List<DividendAnnouncementData> dataList) {
        if (CollectionUtil.isEmpty(dataList)) {
            return;
        }

        int transformedCount = 0;
        for (DividendAnnouncementData data : dataList) {
            // 只处理银行间市场的证券
            if ("89".equals(data.getSecurityMarket()) && StrUtil.isNotBlank(data.getSecurityCode())) {
                try {
                    String originalCode = data.getSecurityCode();
                    String transformedCode = bankReconciliationService.transformCode(originalCode);

                    // 如果转换后的代码与原代码不同，说明进行了转换
                    if (!originalCode.equals(transformedCode)) {
                        log.debug("银行间证券代码转换：{} -> {}", originalCode, transformedCode);
                        data.setSecurityCode(transformedCode);
                        transformedCount++;
                    }
                } catch (Exception e) {
                    log.warn("银行间证券代码转换失败，证券代码：{}，错误：{}", data.getSecurityCode(), e.getMessage());
                }
            }
        }

        if (transformedCount > 0) {
            log.info("银行间证券代码转换完成，共转换 {} 条记录", transformedCount);
        }
    }
}
