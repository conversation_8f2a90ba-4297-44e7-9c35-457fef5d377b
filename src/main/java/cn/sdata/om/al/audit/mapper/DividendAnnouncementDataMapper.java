package cn.sdata.om.al.audit.mapper;

import cn.sdata.om.al.audit.entity.DividendAnnouncementData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分红公告数据Mapper
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Mapper
public interface DividendAnnouncementDataMapper extends BaseMapper<DividendAnnouncementData> {

    /**
     * 根据证券代码和同步日期查询分红公告数据
     *
     * @param securityCodes 证券代码列表
     * @param syncDate 同步日期
     * @return 分红公告数据列表
     */
    List<DividendAnnouncementData> selectBySecurityCodesAndSyncDate(@Param("securityCodes") List<String> securityCodes,
                                                                    @Param("syncDate") String syncDate);

    /**
     * 根据同步日期删除数据
     *
     * @param syncDate 同步日期
     * @return 删除记录数
     */
    int deleteBySyncDate(@Param("syncDate") String syncDate);

    /**
     * 批量插入分红公告数据
     *
     * @param list 数据列表
     * @return 插入记录数
     */
    int batchInsert(@Param("list") List<DividendAnnouncementData> list);

    /**
     * 根据证券代码和日期范围查询分红公告数据
     *
     * @param securityCode 证券代码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分红公告数据列表
     */
    List<DividendAnnouncementData> selectBySecurityCodeAndDateRange(@Param("securityCode") String securityCode,
                                                                    @Param("startDate") String startDate,
                                                                    @Param("endDate") String endDate);

    /**
     * 根据证券代码查询分红公告数据（债券查询除权日>=数据日期，其他查询最新）
     *
     * @param securityCode 证券代码
     * @param dataDate 数据日期
     * @return 分红公告数据
     */
    DividendAnnouncementData selectLatestBySecurityCode(@Param("securityCode") String securityCode, @Param("dataDate") String dataDate);



    /**
     * 根据ID列表查询已存在的数据
     *
     * @param juyuanIds 聚源ID列表
     * @return 已存在的聚源ID列表
     */
    List<String> queryExistingJuyuanIds(@Param("juyuanIds") List<String> juyuanIds);


}
