package cn.sdata.om.al.audit.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.sdata.om.al.audit.entity.DividendAnnouncementData;
import cn.sdata.om.al.audit.service.DividendAnnouncementDataService;
import cn.sdata.om.al.audit.service.DividendAnnouncementSyncService;
import cn.sdata.om.al.result.R;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 分红公告数据控制器
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@RestController
@RequestMapping("/audit/dividend-announcement")
@RequiredArgsConstructor
public class DividendAnnouncementController {

    private final DividendAnnouncementDataService dividendAnnouncementDataService;
    private final DividendAnnouncementSyncService dividendAnnouncementSyncService;

    @PostMapping("/sync")
    public R<String> syncDividendData(@RequestParam(required = false) String syncDate) {
        
        if (StrUtil.isBlank(syncDate)) {
            syncDate = DateUtil.formatDate(new Date());
        }
        
        log.info("手动触发分红公告数据同步，同步日期：{}", syncDate);
        return dividendAnnouncementSyncService.syncJuyuanDividendData(syncDate);
    }

    @GetMapping("/latest/{securityCode}")
    public R<DividendAnnouncementData> getLatestBySecurityCode(@PathVariable String securityCode, @RequestParam String dataDate) {

        DividendAnnouncementData data = dividendAnnouncementDataService.getLatestBySecurityCode(securityCode, dataDate);
        return R.ok(data);
    }

    @PostMapping("/latest/batch")
    public R<List<DividendAnnouncementData>> getLatestBySecurityCodes(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<String> securityCodes = (List<String>) request.get("securityCodes");
        String dataDate = (String) request.get("dataDate");

        List<DividendAnnouncementData> dataList = dividendAnnouncementDataService.getLatestBySecurityCodes(securityCodes, dataDate);
        return R.ok(dataList);
    }

    @GetMapping("/range")
    public R<List<DividendAnnouncementData>> getByDateRange(
            @RequestParam String securityCode,
            @RequestParam String startDate,
            @RequestParam String endDate) {
        
        List<DividendAnnouncementData> dataList = dividendAnnouncementDataService
                .getBySecurityCodeAndDateRange(securityCode, startDate, endDate);
        return R.ok(dataList);
    }

    @DeleteMapping("/clean")
    public R<String> cleanExpiredData(@RequestParam String beforeDate) {

        return dividendAnnouncementDataService.cleanExpiredData(beforeDate);
    }

    @PostMapping("/sync/bond")
    public R<String> syncAllBondDividendData() {
        return dividendAnnouncementSyncService.syncAllBondDividendData();
    }

    @GetMapping("/sync-date/{syncDate}")
    public R<List<DividendAnnouncementData>> getBySyncDate(
            @PathVariable String syncDate,
            @RequestParam(required = false) List<String> securityCodes) {
        
        List<DividendAnnouncementData> dataList = dividendAnnouncementDataService
                .getBySecurityCodesAndSyncDate(securityCodes, syncDate);
        return R.ok(dataList);
    }
}
