package cn.sdata.om.al.audit.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.audit.entity.DividendAnnouncementData;
import cn.sdata.om.al.audit.mapper.DividendAnnouncementDataMapper;
import cn.sdata.om.al.audit.service.DividendAnnouncementDataService;
import cn.sdata.om.al.audit.service.DividendAnnouncementSyncService;
import cn.sdata.om.al.result.R;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 分红公告数据服务实现类
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DividendAnnouncementDataServiceImpl extends ServiceImpl<DividendAnnouncementDataMapper, DividendAnnouncementData>
        implements DividendAnnouncementDataService {

    private final DividendAnnouncementSyncService dividendAnnouncementSyncService;

    @Override
    public R<String> syncJuyuanData(String syncDate) {
        return dividendAnnouncementSyncService.syncJuyuanDividendData(syncDate);
    }

    @Override
    public List<DividendAnnouncementData> getBySecurityCodesAndSyncDate(List<String> securityCodes, String syncDate) {
        return this.baseMapper.selectBySecurityCodesAndSyncDate(securityCodes, syncDate);
    }

    @Override
    public List<DividendAnnouncementData> getBySecurityCodeAndDateRange(String securityCode, String startDate, String endDate) {
        return this.baseMapper.selectBySecurityCodeAndDateRange(securityCode, startDate, endDate);
    }

    @Override
    public DividendAnnouncementData getLatestBySecurityCode(String securityCode, String dataDate) {
        return this.baseMapper.selectLatestBySecurityCode(securityCode, dataDate);
    }

    @Override
    public List<DividendAnnouncementData> getLatestBySecurityCodes(List<String> securityCodes, String dataDate) {
        if (CollectionUtil.isEmpty(securityCodes)) {
            return List.of();
        }

        // 超高性能查询：逐个查询每个证券代码的分红记录
        List<DividendAnnouncementData> result = new ArrayList<>();

        for (String securityCode : securityCodes) {
            try {
                DividendAnnouncementData data = this.baseMapper.selectLatestBySecurityCode(securityCode, dataDate);
                if (data != null) {
                    result.add(data);
                }
            } catch (Exception e) {
                log.warn("查询证券代码 {} 的分红数据失败: {}", securityCode, e.getMessage());
            }
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> cleanExpiredData(String beforeDate) {
        try {
            LambdaQueryWrapper<DividendAnnouncementData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.lt(DividendAnnouncementData::getSyncDate, beforeDate);

            int deletedCount = this.baseMapper.delete(queryWrapper);
            log.info("清理过期分红公告数据完成，清理记录数：{}", deletedCount);

            return R.ok("清理完成，清理记录数：" + deletedCount);
        } catch (Exception e) {
            log.error("清理过期分红公告数据失败", e);
            return R.failed("清理失败：" + e.getMessage());
        }
    }
}
