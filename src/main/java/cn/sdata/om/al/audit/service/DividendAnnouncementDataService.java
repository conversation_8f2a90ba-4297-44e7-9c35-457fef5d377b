package cn.sdata.om.al.audit.service;

import cn.sdata.om.al.audit.entity.DividendAnnouncementData;
import cn.sdata.om.al.result.R;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 分红公告数据服务接口
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface DividendAnnouncementDataService extends IService<DividendAnnouncementData> {

    /**
     * 同步聚源分红公告数据
     *
     * @param syncDate 同步日期
     * @return 同步结果
     */
    R<String> syncJuyuanData(String syncDate);

    /**
     * 根据证券代码和同步日期查询分红公告数据
     *
     * @param securityCodes 证券代码列表
     * @param syncDate 同步日期
     * @return 分红公告数据列表
     */
    List<DividendAnnouncementData> getBySecurityCodesAndSyncDate(List<String> securityCodes, String syncDate);

    /**
     * 根据证券代码和日期范围查询分红公告数据
     *
     * @param securityCode 证券代码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分红公告数据列表
     */
    List<DividendAnnouncementData> getBySecurityCodeAndDateRange(String securityCode, String startDate, String endDate);

    /**
     * 根据证券代码查询分红公告数据（债券查询除权日>=数据日期，其他查询最新）
     *
     * @param securityCode 证券代码
     * @param dataDate 数据日期
     * @return 分红公告数据
     */
    DividendAnnouncementData getLatestBySecurityCode(String securityCode, String dataDate);

    /**
     * 根据证券代码列表查询分红公告数据（债券查询除权日>=数据日期，其他查询最新）
     *
     * @param securityCodes 证券代码列表
     * @param dataDate 数据日期
     * @return 分红公告数据列表
     */
    List<DividendAnnouncementData> getLatestBySecurityCodes(List<String> securityCodes, String dataDate);

    /**
     * 清理过期数据
     *
     * @param beforeDate 清理此日期之前的数据
     * @return 清理结果
     */
    R<String> cleanExpiredData(String beforeDate);
}
