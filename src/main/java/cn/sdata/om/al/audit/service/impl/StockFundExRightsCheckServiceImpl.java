package cn.sdata.om.al.audit.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.sdata.om.al.audit.dto.SecurityHoldingDto;
import cn.sdata.om.al.audit.dto.StockFundExRightsCheckQueryDTO;
import cn.sdata.om.al.audit.entity.DividendAnnouncementData;
import cn.sdata.om.al.audit.entity.StockFundExRightsCheck;
import cn.sdata.om.al.audit.enums.FlowCheckStatus;
import cn.sdata.om.al.audit.util.ExRightsCheckRuleUtil;
import cn.sdata.om.al.audit.mapper.StockFundExRightsCheckMapper;

import cn.sdata.om.al.audit.service.DividendAnnouncementDataService;
import cn.sdata.om.al.audit.service.SecurityMarketMappingService;
import cn.sdata.om.al.audit.service.StockFundExRightsCheckService;
import cn.sdata.om.al.service.impl.BankReconciliationServiceImpl;
import cn.sdata.om.al.audit.service.StockFundExRightsCheckSyncTimeService;
import cn.sdata.om.al.audit.vo.StockFundExRightsCheckVO;
import cn.sdata.om.al.entity.AccountInformation;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.mapper.ValuationDBMapper;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.AccountInformationService;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 股票和开基除权价格检查服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StockFundExRightsCheckServiceImpl extends ServiceImpl<StockFundExRightsCheckMapper, StockFundExRightsCheck> 
        implements StockFundExRightsCheckService {

    private final ValuationDBMapper valuationDBMapper;
    private final DividendAnnouncementDataService dividendAnnouncementDataService;
    private final SecurityMarketMappingService securityMarketMappingService;
    private final AccountInformationService accountInformationService;
    private final StockFundExRightsCheckSyncTimeService syncTimeService;
    private final BankReconciliationServiceImpl bankReconciliationService;

    @Override
    public Page<StockFundExRightsCheckVO> selectPageInfo(StockFundExRightsCheckQueryDTO query) {
        Page<StockFundExRightsCheckVO> page = new Page<>(query.getCurrent(), query.getSize());
        return this.baseMapper.selectPageInfo(page, query);
    }

    @Override
    public R<String> executeExRightsCheck(String dataDate) {
        try {
            log.info("开始执行股票和开基除权价格检查，数据日期：{}", dataDate);

            // 1. 同步聚源数据（优化后应该很快）
            log.info("开始同步聚源数据");
            R<String> syncResult = syncJuyuanExRightsData(dataDate);
            if (!syncResult.isSuccess()) {
                log.warn("聚源数据同步失败：{}", syncResult.getMessage());
            }

            // 2. 查询估值系统证券持仓数据
            log.info("开始查询估值系统证券持仓数据，日期：{}", dataDate);
            long startTime = System.currentTimeMillis();

            List<SecurityHoldingDto> holdingList = valuationDBMapper.selectSecurityHoldingForExRightsCheck(dataDate);

            long endTime = System.currentTimeMillis();
            log.info("估值系统证券持仓数据查询完成，耗时：{}ms，数据量：{} 条", endTime - startTime,
                    holdingList != null ? holdingList.size() : 0);

            if (CollectionUtil.isEmpty(holdingList)) {
                return R.ok("当日无持仓数据，检查完成");
            }

            // 3. 获取证券代码列表
            List<String> securityCodes = holdingList.stream()
                    .map(SecurityHoldingDto::getSecurityCode)
                    .distinct()
                    .collect(Collectors.toList());

            // 4. 查询分红公告数据（只查询持仓证券的分红数据）
            log.info("开始查询持仓证券的分红公告数据，证券代码数量：{}", securityCodes.size());
            List<DividendAnnouncementData> dividendDataList = getLatestDividendDataForHoldings(securityCodes, dataDate);

            // 5. 生成检查数据
            Map<String, String> idNames = accountInformationService.list().stream().collect(Collectors.toMap(AccountInformation::getId, AccountInformation::getFullProductName, (oldOne, newOne) -> newOne));
            List<StockFundExRightsCheck> checkList = new ArrayList<>();
            int processedCount = 0;
            int matchedCount = 0;

            for (SecurityHoldingDto holding : holdingList) {
                processedCount++;
                String securityCode = holding.getSecurityCode();
                String productId = holding.getProductId();
                String productName = idNames.get(productId);
                holding.setProductName(productName);

                // 根据证券代码和市场查找对应的分红公告数据
                String securityMarket = holding.getSecurityMarket();
                log.info("处理第{}/{}条持仓数据：产品ID={}, 证券代码={}, 证券名称={}, 证券类型={}, 市场代码={}",
                        processedCount, holdingList.size(), productId, securityCode, holding.getSecurityName(),
                        holding.getSecurityType(), securityMarket);

                DividendAnnouncementData dividendData = findMatchingDividendData(securityCode, securityMarket, dividendDataList);

                if (dividendData != null) {
                    matchedCount++;
                    log.info("匹配成功！第{}/{}条，累计匹配{}条，分红数据：聚源ID={}, 除权日={}, 数据源类型={}",
                            processedCount, holdingList.size(), matchedCount, dividendData.getJuyuanId(),
                            dividendData.getExRightsDate(), dividendData.getDataSourceType());
                } else {
                    log.warn("匹配失败！第{}/{}条，证券代码={}, 市场代码={}",
                            processedCount, holdingList.size(), securityCode, securityMarket);
                }


                StockFundExRightsCheck checkData = buildCheckData(holding, dividendData, dataDate);
                
                // 执行流水检查
                performFlowCheck(checkData, dataDate);
                
                checkList.add(checkData);
            }

            log.info("持仓数据处理完成，总持仓数：{}，匹配到分红数据：{}，匹配率：{:.2f}%",
                    holdingList.size(), matchedCount, (double) matchedCount / holdingList.size() * 100);

            // 6. 批量保存检查结果
            if (CollectionUtil.isNotEmpty(checkList)) {
                ((StockFundExRightsCheckServiceImpl) AopContext.currentProxy()).saveCheckResultsInTransaction(checkList, dataDate);
            }

            // 7. 记录同步时间
            syncTimeService.recordCurrentSyncTime(dataDate);

            log.info("股票和开基除权价格检查完成，处理记录数：{}", checkList.size());
            return R.ok("检查完成，处理记录数：" + checkList.size());

        } catch (Exception e) {
            log.error("执行股票和开基除权价格检查失败", e);
            return R.failed("检查执行失败：" + e.getMessage());
        }
    }

    @Override
    public R<String> syncJuyuanExRightsData(String dataDate) {
        return dividendAnnouncementDataService.syncJuyuanData(dataDate);
    }

    @Override
    public Long getSummaryStatistics(String startDate, String endDate, String valuationTime) {
        return this.baseMapper.getSummaryStatistics(startDate, endDate, valuationTime);
    }

    @Override
    public Map<String, Object> getAbnormalStatistics(String dataDate) {
        return this.baseMapper.getAbnormalStatistics(dataDate);
    }

    @Override
    public R<String> recheckById(String id) {
        try {
            StockFundExRightsCheck checkData = this.getById(id);
            if (checkData == null) {
                return R.failed("检查记录不存在");
            }

            // 重新执行流水检查
            performFlowCheck(checkData, checkData.getDataDate());
            
            // 更新记录
            checkData.setUpdateTime(LocalDateTime.now());
            this.updateById(checkData);

            return R.ok("重新检查完成");
        } catch (Exception e) {
            log.error("重新检查失败", e);
            return R.failed("重新检查失败：" + e.getMessage());
        }
    }

    @Override
    public R<String> batchRecheck(String dataDate) {
        return executeExRightsCheck(dataDate);
    }

    /**
     * 构建检查数据
     */
    private StockFundExRightsCheck buildCheckData(SecurityHoldingDto holding, DividendAnnouncementData dividendData, String dataDate) {
        StockFundExRightsCheck checkData = new StockFundExRightsCheck();
        checkData.setId(IdWorker.getIdStr());
        checkData.setDataDate(dataDate);
        checkData.setProductId(holding.getProductId());
        checkData.setProductName(holding.getProductName()); // 产品名称需要从其他地方获取
        checkData.setSecurityType(holding.getSecurityType());
        checkData.setSecurityTypeName(holding.getSecurityTypeName());
        checkData.setSecurityName(holding.getSecurityName());
        checkData.setSecurityCode(holding.getSecurityCode());
        checkData.setSecurityMarket(holding.getSecurityMarket());
        checkData.setSecurityMarketName(holding.getSecurityMarketName());
        checkData.setSecurityInternalCode(holding.getSecurityInternalCode());

        checkData.setCreateTime(LocalDateTime.now());
        checkData.setUpdateTime(LocalDateTime.now());

        // 设置分红公告数据
        if (dividendData != null) {
            checkData.setRegistrationDate(dividendData.getRegistrationDate() != null ? dividendData.getRegistrationDate().toString() : null);
            checkData.setExRightsDate(dividendData.getExRightsDate() != null ? dividendData.getExRightsDate().toString() : null);
            checkData.setBonusSharePaymentDate(dividendData.getBonusSharePaymentDate() != null ? dividendData.getBonusSharePaymentDate().toString() : null);
            checkData.setDividendPaymentDate(dividendData.getDividendPaymentDate() != null ? dividendData.getDividendPaymentDate().toString() : null);
            checkData.setBonusShareRatio(dividendData.getBonusShareRatio());
            checkData.setCashDividendRatio(dividendData.getCashDividendRatio());
        }

        return checkData;
    }

    /**
     * 根据证券代码和市场查找匹配的分红公告数据
     */
    private DividendAnnouncementData findMatchingDividendData(String securityCode, String securityMarket,
                                                              List<DividendAnnouncementData> dividendDataList) {
        log.info("开始匹配分红数据：估值证券代码={}, 估值市场代码={}, 分红数据总数={}",
                securityCode, securityMarket, dividendDataList != null ? dividendDataList.size() : 0);

        if (CollectionUtil.isEmpty(dividendDataList)) {
            log.warn("分红数据列表为空，无法匹配");
            return null;
        }

        // 1. 如果有市场代码，优先按证券代码+市场代码匹配
        if (StrUtil.isNotBlank(securityMarket)) {
            log.info("有市场代码，开始按市场代码匹配：估值市场代码={}", securityMarket);

            // 将估值市场代码转换为聚源市场代码
            String juyuanMarketCode = securityMarketMappingService.convertValuationToJuyuanMarket(securityMarket);
            log.info("市场代码转换：估值市场代码={} -> 聚源市场代码={}", securityMarket, juyuanMarketCode);

            if (StrUtil.isNotBlank(juyuanMarketCode)) {
                // 判断是否为银行间市场（估值市场代码为"3"，聚源市场代码为"89"）
                boolean isBankMarket = "3".equals(securityMarket) || "89".equals(juyuanMarketCode);
                log.info("是否银行间市场：{} (估值市场代码={}, 聚源市场代码={})", isBankMarket, securityMarket, juyuanMarketCode);

                int matchAttempts = 0;
                for (DividendAnnouncementData data : dividendDataList) {
                    matchAttempts++;
                    log.debug("尝试匹配第{}条分红数据：聚源证券代码={}, 聚源市场代码={}, 数据源类型={}",
                            matchAttempts, data.getSecurityCode(), data.getSecurityMarket(), data.getDataSourceType());

                    // 如果是银行间数据，需要将聚源的证券代码通过transformCode转换后再匹配
                    if (isBankMarket) {
                        String transformedJuyuanCode = bankReconciliationService.transformCode(data.getSecurityCode());
                        log.info("银行间数据匹配：估值代码={}, 聚源原始代码={}, 聚源转换后代码={}, 聚源市场代码={}, 期望市场代码={}",
                                securityCode, data.getSecurityCode(), transformedJuyuanCode, data.getSecurityMarket(), juyuanMarketCode);

                        if (securityCode.equals(transformedJuyuanCode) &&
                            juyuanMarketCode.equals(data.getSecurityMarket())) {
                            log.info("银行间数据匹配成功！返回数据：{}", data.getJuyuanId());
                            return data;
                        }
                    } else {
                        // 非银行间数据，直接匹配
                        log.debug("非银行间数据匹配：估值代码={}, 聚源代码={}, 聚源市场代码={}, 期望市场代码={}",
                                securityCode, data.getSecurityCode(), data.getSecurityMarket(), juyuanMarketCode);

                        if (securityCode.equals(data.getSecurityCode()) &&
                            juyuanMarketCode.equals(data.getSecurityMarket())) {
                            log.info("非银行间数据匹配成功！返回数据：{}", data.getJuyuanId());
                            return data;
                        }
                    }
                }
                log.warn("按市场代码匹配失败，尝试了{}条分红数据", matchAttempts);
            } else {
                log.warn("估值市场代码转换为聚源市场代码失败：{}", securityMarket);
            }
        } else {
            log.info("无市场代码，将进行通用匹配");
        }

        // 2. 如果按市场代码匹配不到，或者市场代码为空，则只按证券代码匹配
        log.info("开始通用匹配（不限制市场代码）");

        // 这里也需要考虑银行间数据的转换
        boolean isBankMarket = "3".equals(securityMarket);
        log.info("通用匹配中是否银行间市场：{} (估值市场代码={})", isBankMarket, securityMarket);

        int generalMatchAttempts = 0;
        for (DividendAnnouncementData data : dividendDataList) {
            generalMatchAttempts++;
            log.debug("通用匹配第{}条：聚源证券代码={}, 聚源市场代码={}, 数据源类型={}",
                    generalMatchAttempts, data.getSecurityCode(), data.getSecurityMarket(), data.getDataSourceType());

            if (isBankMarket && "89".equals(data.getSecurityMarket())) {
                // 银行间数据，使用转换后的代码匹配
                String transformedJuyuanCode = bankReconciliationService.transformCode(data.getSecurityCode());
                log.info("银行间数据通用匹配：估值代码={}, 聚源原始代码={}, 聚源转换后代码={}",
                        securityCode, data.getSecurityCode(), transformedJuyuanCode);

                if (securityCode.equals(transformedJuyuanCode)) {
                    log.info("银行间数据通用匹配成功！返回数据：{}", data.getJuyuanId());
                    return data;
                }
            } else if (!isBankMarket) {
                // 非银行间数据，直接匹配
                log.debug("非银行间数据通用匹配：估值代码={}, 聚源代码={}", securityCode, data.getSecurityCode());

                if (securityCode.equals(data.getSecurityCode())) {
                    log.info("非银行间数据通用匹配成功！返回数据：{}", data.getJuyuanId());
                    return data;
                }
            }
        }

        log.warn("所有匹配尝试都失败了，通用匹配尝试了{}条数据", generalMatchAttempts);

        // 3. 添加详细的调试信息，显示所有可用的分红数据
        log.info("匹配失败，显示所有可用的分红数据供调试：");
        for (int i = 0; i < Math.min(dividendDataList.size(), 10); i++) {
            DividendAnnouncementData data = dividendDataList.get(i);
            log.info("分红数据[{}]：聚源ID={}, 证券代码={}, 市场代码={}, 数据源类型={}, 除权日={}",
                    i, data.getJuyuanId(), data.getSecurityCode(), data.getSecurityMarket(),
                    data.getDataSourceType(), data.getExRightsDate());
        }
        if (dividendDataList.size() > 10) {
            log.info("还有{}条分红数据未显示...", dividendDataList.size() - 10);
        }

        return null;
    }

    /**
     * 执行流水检查
     */
    private void performFlowCheck(StockFundExRightsCheck checkData, String dataDate) {
        String securityCode = checkData.getSecurityCode();
        String productId = checkData.getProductId();

        // 除权流水检查
        checkExRightsFlow(checkData, securityCode, productId, dataDate);

        // 红股流水检查
        checkBonusShareFlow(checkData, securityCode, productId, dataDate);

        // 红利发放流水检查
        checkDividendFlow(checkData, securityCode, productId, dataDate);
    }

    /**
     * 检查除权流水
     */
    private void checkExRightsFlow(StockFundExRightsCheck checkData, String securityCode, String productId, String dataDate) {
        // 如果除权日为空，跳过检查，设置为正常状态
        if (StrUtil.isBlank(checkData.getExRightsDate())) {
            checkData.setExRightsFlowCheck(FlowCheckStatus.NORMAL);
            log.info("除权流水检查 - 证券代码: {}, 证券名称: {}, 产品ID: {}, 除权日为空，跳过检查，设置为正常状态",
                    securityCode, checkData.getSecurityName(), productId);
            return;
        }

        List<Map<String, Object>> flowList = valuationDBMapper.selectExRightsFlow(securityCode, productId, dataDate);

        boolean hasFlow = CollectionUtil.isNotEmpty(flowList);
        checkData.setExRightsFlowExists(hasFlow);

        // 使用业务规则工具类判断状态
        FlowCheckStatus status = ExRightsCheckRuleUtil.checkExRightsFlow(hasFlow, dataDate, checkData.getExRightsDate());
        checkData.setExRightsFlowCheck(status);

        // 输出流水检查日志
        log.info("除权流水检查 - 证券代码: {}, 证券名称: {}, 产品ID: {}, 除权日: {}, 流水存在: {}, 检查结果: {}",
                securityCode, checkData.getSecurityName(), productId, checkData.getExRightsDate(),
                hasFlow, status.getCode());
    }

    /**
     * 检查红股流水
     */
    private void checkBonusShareFlow(StockFundExRightsCheck checkData, String securityCode, String productId, String dataDate) {
        // 如果除权日为空，跳过检查，设置为正常状态（红股流水以除权日为基准）
        if (StrUtil.isBlank(checkData.getExRightsDate())) {
            checkData.setBonusShareFlowCheck(FlowCheckStatus.NORMAL);
            log.info("红股流水检查 - 证券代码: {}, 证券名称: {}, 产品ID: {}, 除权日为空，跳过检查，设置为正常状态",
                    securityCode, checkData.getSecurityName(), productId);
            return;
        }

        List<Map<String, Object>> flowList = valuationDBMapper.selectBonusShareFlow(securityCode, productId, dataDate);

        boolean hasFlow = CollectionUtil.isNotEmpty(flowList);
        checkData.setBonusShareFlowExists(hasFlow);

        // 使用业务规则工具类判断状态（红股流水使用除权日进行检查）
        FlowCheckStatus status = ExRightsCheckRuleUtil.checkBonusShareFlow(hasFlow, dataDate, checkData.getExRightsDate());
        checkData.setBonusShareFlowCheck(status);

        // 输出流水检查日志
        log.info("红股流水检查 - 证券代码: {}, 证券名称: {}, 产品ID: {}, 除权日: {}, 红股比例: {}, 流水存在: {}, 检查结果: {}",
                securityCode, checkData.getSecurityName(), productId, checkData.getExRightsDate(),
                checkData.getBonusShareRatio(), hasFlow, status.getCode());
    }

    /**
     * 检查红利发放流水
     */
    private void checkDividendFlow(StockFundExRightsCheck checkData, String securityCode, String productId, String dataDate) {
        // 如果红利发放日为空，跳过检查，设置为正常状态
        if (StrUtil.isBlank(checkData.getDividendPaymentDate())) {
            checkData.setDividendFlowCheck(FlowCheckStatus.NORMAL);
            log.info("红利发放流水检查 - 证券代码: {}, 证券名称: {}, 产品ID: {}, 红利发放日为空，跳过检查，设置为正常状态",
                    securityCode, checkData.getSecurityName(), productId);
            return;
        }

        List<Map<String, Object>> flowList = valuationDBMapper.selectDividendFlow(securityCode, productId, dataDate);

        boolean hasFlow = CollectionUtil.isNotEmpty(flowList);
        checkData.setDividendFlowExists(hasFlow);

        // 使用业务规则工具类判断状态
        FlowCheckStatus status = ExRightsCheckRuleUtil.checkDividendFlow(hasFlow, dataDate, checkData.getDividendPaymentDate());
        checkData.setDividendFlowCheck(status);

        // 输出流水检查日志
        log.info("红利发放流水检查 - 证券代码: {}, 证券名称: {}, 产品ID: {}, 红利发放日: {}, 现金分红比例: {}, 流水存在: {}, 检查结果: {}",
                securityCode, checkData.getSecurityName(), productId, checkData.getDividendPaymentDate(),
                checkData.getCashDividendRatio(), hasFlow, status.getCode());
    }

    /**
     * 在事务中保存检查结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCheckResultsInTransaction(List<StockFundExRightsCheck> checkList, String dataDate) {
        // 删除当日已有数据
        this.baseMapper.deleteByDataDate(dataDate);

        // 批量插入新数据
        this.baseMapper.batchInsert(checkList);
    }

    @Override
    public List<CommonEntity> getProductIds() {
        return this.baseMapper.getProductIds();
    }

    @Override
    public List<CommonEntity> getSecurityTypesByProductIds(List<String> productIds) {
        return this.baseMapper.getSecurityTypesByProductIds(productIds);
    }

    @Override
    public List<CommonEntity> getSecurityCodesByConditions(List<String> productIds, List<String> securityTypes) {
        return this.baseMapper.getSecurityCodesByConditions(productIds, securityTypes);
    }

    /**
     * 获取持仓证券的分红数据（债券查询除权日>=数据日期，其他查询最新）
     */
    private List<DividendAnnouncementData> getLatestDividendDataForHoldings(List<String> securityCodes, String dataDate) {
        if (CollectionUtil.isEmpty(securityCodes)) {
            return new ArrayList<>();
        }

        log.info("开始查询持仓证券分红数据，证券代码数量：{}，数据日期：{}", securityCodes.size(), dataDate);
        long startTime = System.currentTimeMillis();

        // 直接查询所有证券代码的分红数据（债券按除权日过滤，其他查最新）
        try {
            List<DividendAnnouncementData> allResults = dividendAnnouncementDataService.getLatestBySecurityCodes(securityCodes, dataDate);
            log.info("分红数据查询完成，获得数据：{} 条", allResults.size());

            // 统计各种数据源类型的数量
            Map<String, Long> dataSourceCount = allResults.stream()
                    .collect(Collectors.groupingBy(
                            data -> data.getDataSourceType() != null ? data.getDataSourceType() : "NULL",
                            Collectors.counting()
                    ));
            log.info("分红数据按数据源类型统计：{}", dataSourceCount);

            // 统计各种市场代码的数量
            Map<String, Long> marketCount = allResults.stream()
                    .collect(Collectors.groupingBy(
                            data -> data.getSecurityMarket() != null ? data.getSecurityMarket() : "NULL",
                            Collectors.counting()
                    ));
            log.info("分红数据按市场代码统计：{}", marketCount);

            long endTime = System.currentTimeMillis();
            log.info("持仓证券分红数据查询完成，总耗时：{}ms，查询到数据：{} 条", endTime - startTime, allResults.size());

            return allResults;
        } catch (Exception e) {
            log.error("查询分红数据失败：{}", e.getMessage());
            return new ArrayList<>();
        }
    }


}
