package cn.sdata.om.al.config;

import io.github.resilience4j.core.IntervalFunction;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.time.Duration;

@Configuration
@Slf4j
public class RateLimiterConfiguration {

    @Bean
    public RateLimiter mailRateLimiter() {
        RateLimiterConfig config = RateLimiterConfig.custom()
            .limitRefreshPeriod(Duration.ofSeconds(5))
            .limitForPeriod(1)
            .timeoutDuration(Duration.ofSeconds(60))
            .build();
        return RateLimiter.of("mailRateLimiter", config);
    }

    // 重试配置：最多3次，指数退避
    @Bean
    public Retry mailRetry() {
        RetryConfig config = RetryConfig.custom()
                // 最大尝试次数
                .maxAttempts(3)
                // 初始等待时间
//                .waitDuration(Duration.ofSeconds(2))
                // 指数退避：2s -> 4s -> 8s
                .intervalFunction(IntervalFunction.ofExponentialBackoff(2000, 2))
                // 遇到什么异常才重试
                .retryExceptions(Exception.class)
                .build();
        Retry retry = Retry.of("mailRetry", config);

        // 注册监听器 日志信息
        retry.getEventPublisher()
                .onRetry(event -> log.warn("邮件发送重试 - name={}, attempt={}, lastThrowable={}",
                        event.getName(),
                        event.getNumberOfRetryAttempts(),
                        event.getLastThrowable() != null ? event.getLastThrowable().getMessage() : "null",
                        event.getLastThrowable()))
                .onError(event -> log.error("邮件发送失败 - name={}, attempts={}",
                        event.getName(), event.getNumberOfRetryAttempts()))
                .onSuccess(event -> log.info("邮件发送成功 - name={}, attempts={}",
                        event.getName(), event.getNumberOfRetryAttempts()));

        return retry;
    }
}
